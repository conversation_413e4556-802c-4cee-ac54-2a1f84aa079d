.fl {
  float: left;
}
.fr {
  float: right;
}
.cl {
  zoom: 1;
}
.cl:after {
  content: "";
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.flex {
  display: flex;
}
.href {
  color: #0000FF;
  cursor: pointer;
}
ul,
ul li {
  list-style: none;
}
.mt_1 {
  margin-top: 10px;
}
.mr_1 {
  margin-right: 10px;
}
.mb_1 {
  margin-bottom: 10px;
}
.ml_1 {
  margin-left: 10px;
}
.m_1 {
  margin: 10px;
}
.pt_1 {
  padding-top: 10px;
}
.pr_1 {
  padding-right: 10px;
}
.pb_1 {
  padding-bottom: 10px;
}
.pl_1 {
  padding-left: 10px;
}
.p_1 {
  padding: 10px;
}
.mt_2 {
  margin-top: 20px;
}
.mr_2 {
  margin-right: 20px;
}
.mb_2 {
  margin-bottom: 20px;
}
.ml_2 {
  margin-left: 20px;
}
.m_2 {
  margin: 20px;
}
.pt_2 {
  padding-top: 20px;
}
.pr_2 {
  padding-right: 20px;
}
.pb_2 {
  padding-bottom: 20px;
}
.pl_2 {
  padding-left: 20px;
}
.p_2 {
  padding: 20px;
}
.mt_3 {
  margin-top: 30px;
}
.mr_3 {
  margin-right: 30px;
}
.mb_3 {
  margin-bottom: 30px;
}
.ml_3 {
  margin-left: 30px;
}
.m_3 {
  margin: 30px;
}
.pt_3 {
  padding-top: 30px;
}
.pr_3 {
  padding-right: 30px;
}
.pb_3 {
  padding-bottom: 30px;
}
.pl_3 {
  padding-left: 30px;
}
.p_3 {
  padding: 30px;
}
.mt_4 {
  margin-top: 40px;
}
.mr_4 {
  margin-right: 40px;
}
.mb_4 {
  margin-bottom: 40px;
}
.ml_4 {
  margin-left: 40px;
}
.m_4 {
  margin: 40px;
}
.pt_4 {
  padding-top: 40px;
}
.pr_4 {
  padding-right: 40px;
}
.pb_4 {
  padding-bottom: 40px;
}
.pl_4 {
  padding-left: 40px;
}
.p_4 {
  padding: 40px;
}
.el-input-box {
  display: table;
}
.h-title {
  text-align: center;
  font-size: 20px;
  line-height: 40px;
  margin-bottom: 20px;
}
.img-box {
  margin: 0 20px 20px 0;
}
.img-box img {
  width: 200px;
  height: 200px;
  display: block;
}
.h-label {
  width: 100px;
  border: 1px solid #dcdfe6;
  padding: 12px 20px;
  cursor: pointer;
  margin-right: 15px;
  box-sizing: border-box;
  border-radius: 4px;
  line-height: 1;
  text-align: right;
  float: left;
  font-size: 14px;
  color: #606266;
  transition: 0.1s;
  -webkit-appearance: none;
  outline: 0;
  display: inline-block;
}
.inline-block {
  display: inline-block;
}
.imgbox {
  overflow: hidden;
}
.imgbox li {
  width: 148px;
  height: 148px;
  margin: 0 8px 8px 0;
  float: left;
}
.imgbox li img {
  border-radius: 6px;
  width: 100%;
  height: 100%;
  cursor: pointer;
}
.h-tip {
  color: #f56c6c;
  font-size: 12px;
  margin-bottom: 10px;
}
.h-limit {
  position: absolute;
  right: 10px;
  top: 0;
  color: #e2e2e2;
}
.avatar-uploader > div {
  background-color: #fbfdff;
  border: 1px dashed #c0ccda;
  border-radius: 6px;
  box-sizing: border-box;
  width: 148px;
  height: 148px;
  line-height: 146px;
  vertical-align: top;
}
.avatar-uploader {
  margin-right: 20px;
  float: left;
}
.avatar-uploader img {
  width: 100%;
  height: 100%;
}
.el-upload video {
  width: 100%;
  height: 100%;
}
.el-date-editor--daterange.el-input__inner {
  width: 100%;
}
.white-bg {
  background: #fff;
  padding: 10px;
  margin-bottom: 10px;
  overflow: hidden;
}
.el-select {
  width: 100%;
}
.search-box {
  padding: 10px 10px 0 10px;
}
.search-box .el-col {
  height: 43px;
}
.search-box .el-form-item {
  margin-bottom: 10px;
}
.search-box .toggle-btn {
  font-size: 12px;
  color: #409EFF;
  margin-left: 10px;
  cursor: pointer;
}
.search-box .toggle-btn i {
  margin-left: 5px;
}
.h-btn {
  cursor: pointer;
  font-size: 18px;
  display: inline-block;
  width: 28px;
  height: 28px;
  line-height: 28px;
}
.separation-bar {
  height: 10px;
  background-color: #e5e9f2;
}
.h-dialog {
  border-radius: 5px;
}
.el-form-item .el-input,
.el-form-item .el-autocomplete {
  max-width: 300px;
}
.el-form-item .el-date-picker {
  max-width: 600px;
  height: 43px;
}
.h-table {
  border-left: 1px solid #ebeef5;
  border-top: 1px solid #ebeef5;
  width: 100%;
  font-size: 14px;
}
.h-table tr td {
  border-bottom: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
  padding: 5px;
  word-break: break-all;
}
.h-table tr td.name {
  width: 200px;
}
.h-table .require {
  color: red;
}
.button-bar {
  margin-right: -10px;
}
.button-bar .el-button.fr {
  margin-left: 0px;
  margin-right: 10px;
}
.block-section {
  border: 1px solid #e4e4e4;
  border-radius: 5px;
}
.block-section .block-section-title {
  padding-left: 10px;
  line-height: 40px;
  border-bottom: 1px solid #e4e4e4;
}
.block-section .block-section-title i {
  height: 40px;
  text-align: center;
  line-height: 40px;
  cursor: pointer;
}
.block-section .block-section-content {
  padding: 20px 10px;
}
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
}
.maxSelect .el-input {
  max-width: 100%!important;
}
.property-cont {
  display: block;
  cursor: pointer;
  height: 23px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.h-table tr td {
  padding: 0;
}
.h-table .el-input__inner {
  padding: 0 5px;
  border: none;
}
.h-table .cell,
.h-table th div {
  padding: 0;
}
.fix-box {
  z-index: 100;
  position: fixed;
  bottom: 0;
  padding-right: 20px;
  left: 211px;
  right: 0px;
  transition: all 0.3s;
}
.fix-box > div {
  background: #fff;
  border-top: 1px solid #dcdfe6;
  box-shadow: 0 -5px 12px 0 rgba(0, 0, 0, 0.1);
  padding-right: 10px;
}
.fix-box .fix-box-btn {
  width: 32px;
  height: 32px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
}
.fix-box.cur {
  height: 50px;
}
.fix-box.isCollapse {
  left: 75px;
}
.el-form-item .remark-input {
  max-width: 100%!important;
  width: 100%;
}
.message-content {
  color: #333;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.message-content .read-icon {
  display: inline-block;
  width: 5px;
  height: 5px;
  background: red;
  border-radius: 5px;
  margin-right: 5px;
}
.message-content.isRead {
  color: #999;
}
.message-content.isRead .read-icon {
  background: transparent;
}
.total {
  margin-top: 10px;
}
.total table {
  border-collapse: collapse;
  width: 100%;
  font-size: 14px;
  color: #333;
}
.total table tr td {
  line-height: 30px;
  background: #8f949a;
  color: #fff;
  padding: 5px 10px;
}
.total table tr td.title {
  width: 100px;
}
.download-file {
  color: #409EFF;
  cursor: pointer;
  display: block;
}
.menu-icon {
  width: 24px;
  height: 24px;
}
.red-input .el-input__inner {
  border-color: red!important;
}
.button-bar.needfix {
  position: fixed;
  padding: 10px 0px 10px 10px;
  right: 20px;
  left: 211px;
  background: #fff;
  z-index: 1000;
  top: 60px;
  margin-right: 0!important;
}
.button-bar.isCollapse {
  left: 75px;
}
.el-autocomplete {
  width: 100%;
}
.h-tab {
  overflow: hidden;
  overflow-x: auto;
  box-sizing: border-box;
  height: 41px;
}
.h-tab .h-tab-list {
  white-space: nowrap;
  box-sizing: border-box;
  display: inline-block;
  border-radius: 4px 4px 0 0;
  transition: transform 0.3s;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-bottom: none;
}
.h-tab a {
  cursor: pointer;
  border-right: 1px solid #e4e7ed;
  float: left;
  display: inline-block;
  padding: 0 20px;
  line-height: 40px;
  color: #303133;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.h-tab a.cur {
  color: #409eff;
}
.h-tab a:last-child {
  border-right: none;
}
.status-bar {
  border: 1px solid #d7dae2;
  display: flex;
  min-height: 80px;
  justify-content: space-between;
  overflow: hidden;
  padding: 0 0 0 20px;
  margin: 10px 0 20px;
}
.status-bar .circle {
  width: 16px;
  height: 16px;
  border-radius: 16px;
  margin-right: 10px;
}
.status-bar > a {
  background: red;
  width: 100px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-bar .status1 {
  color: #FE9400;
}
.status-bar .status1 .circle {
  background: #FE9400;
}
.status-bar .status2 {
  color: #1ABC9C;
}
.status-bar .status2 .circle {
  background: #1ABC9C;
}
.status-bar .status3 {
  color: #FF0000;
}
.status-bar .status3 .circle {
  background: #FF0000;
}
.status-bar > * {
  flex-shrink: 0;
}
.status-bar span {
  margin-right: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.ex-company-name {
  font-size: 14px;
  color: #606266;
  line-height: 34px;
}
.el-button--mini {
  padding: 7px 5px;
}
.table-nopadding .cell,
.table-nopadding td {
  padding: 0!important;
}
.table-nopadding .el-form-item {
  margin-bottom: 0;
}
.table-nopadding .el-input__inner {
  border-color: transparent;
  padding: 0 5px;
}
.table-nopadding .el-upload--text {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}
.table-nopadding .is-error .el-upload--text {
  border: 1px solid #F56C6C;
}
.sticky div {
  background: #d0d0d0;
}
.sticky .el-row {
  padding: 5px 0;
}
.el-input-group__append,
.el-input-group__prepend {
  padding: 0 5px;
}
