@l1:10px;
@l2:20px;
@l3:20px;
@l4:20px;
.fl{float: left}
.fr{float: right}
.cl{zoom:1;}
.cl:after{content:"";height:0;line-height:0;display:block;visibility:hidden;clear:both}
.text-center{text-align: center}
.text-right{text-align: right}
.flex{
    display: flex;
}
.href{
    color: #0000FF;
    cursor: pointer;
}
ul,ul li{list-style: none;}
.hkl_loop(@n, @i:1) when (@i <= @n) {
    .mt_@{i} {
        margin-top:(10px * @i);
    }
    .mr_@{i} {
        margin-right:(10px * @i);
    }
    .mb_@{i} {
        margin-bottom:(10px * @i);
    }
    .ml_@{i} {
        margin-left:(10px * @i);
    }
    .m_@{i}{
        margin:(10px * @i);
    }
    .pt_@{i} {
        padding-top:(10px * @i);
    }
    .pr_@{i} {
        padding-right:(10px * @i);
    }
    .pb_@{i} {
        padding-bottom:(10px * @i);
    }
    .pl_@{i} {
        padding-left:(10px * @i);
    }
    .p_@{i}{
        padding:(10px * @i);
    }
    .hkl_loop(@n, (@i + 1));
}
.hkl_loop(4);

.el-input-box{
    display: table;
}
.el-table{
    //tr td,tr th,tr td .cell{white-space: nowrap;}
}

.h-title{text-align: center;font-size: 20px;line-height: 40px;margin-bottom: 20px}
.img-box{
    margin:0 20px 20px 0;
    img{width: 200px;height: 200px;display: block}
}
.h-label{
    width: 100px;
    border: 1px solid #dcdfe6;
    padding: 12px 20px;
    cursor: pointer;
    margin-right: 15px;
    box-sizing: border-box;
    border-radius: 4px;
    line-height: 1;
    text-align: right;
    float: left;
    font-size: 14px;
    color: #606266;
    transition: .1s;
    -webkit-appearance: none;
    outline:0;
    display: inline-block;
}
.inline-block{
    display: inline-block;
}

.imgbox{
    overflow: hidden;
    li{
        width: 148px;height: 148px;margin: 0 8px 8px 0;float: left;
        img{border-radius: 6px;width: 100%;height: 100%;cursor: pointer}
    }
}
.h-tip{color: #f56c6c;font-size: 12px;margin-bottom: 10px;}
.h-limit{position: absolute;right: 10px;top:0;color: #e2e2e2}
.avatar-uploader>div{
    background-color: #fbfdff;
    border: 1px dashed #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 148px;
    height: 148px;
    line-height: 146px;
    vertical-align: top;
}
.avatar-uploader{
    margin-right: 20px;float: left;
    img{width: 100%;height: 100%;}
}
.el-upload{
    video{width: 100%;height: 100%;}
}
.el-date-editor--daterange.el-input__inner{width: 100%}
.white-bg{background: #fff;padding: 10px;margin-bottom: 10px;overflow: hidden}
.el-select{width: 100%;}
.search-box{
    padding: 10px 10px 0 10px;
    .el-col{height: 43px}
    .el-form-item{margin-bottom: 10px}
    .toggle-btn{
        font-size: 12px;color: #409EFF;margin-left: 10px;cursor: pointer;
        i{margin-left: 5px}
    }
}
.h-btn{cursor: pointer;font-size: 18px;display: inline-block;width: 28px;height: 28px;line-height: 28px;}
.separation-bar {
    height: 10px;
    background-color: #e5e9f2;
}
.h-dialog{border-radius: 5px}
.el-form-item{
    .el-input, .el-autocomplete{max-width: 300px}
    .el-date-picker{max-width: 600px;height: 43px}
}
.h-table{
    border-left: 1px solid #ebeef5;border-top: 1px solid #ebeef5;width: 100%;
    font-size: 14px;
    tr td{border-bottom: 1px solid #ebeef5;border-right: 1px solid #ebeef5;padding: 5px;word-break: break-all;}
    tr td.name{width: 200px}
    .require{color: red;}
}

.button-bar{
    margin-right: -10px;
    .el-button.fr{margin-left: 0px;margin-right: 10px}
}
.block-section{
    border: 1px solid rgb(228, 228, 228);
    border-radius:5px;
    .block-section-title{
        i{height: 40px;text-align: center;line-height: 40px;cursor: pointer}
        padding-left: 10px;
        line-height: 40px;
        border-bottom: 1px solid rgb(228, 228, 228);
    }
    .block-section-content{
        padding: 20px 10px;
    }
}
.el-date-editor.el-input, .el-date-editor.el-input__inner{width: 100%}
.tabel-input{
    //.el-input__inner{border: none}
}

.maxSelect .el-input{max-width: 100%!important;}

.property-cont{display: block;cursor: pointer;height: 23px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap}


.h-table{
    tr td{padding: 0;}
    .el-input__inner{padding: 0 5px;border: none}
    .cell,th div{padding: 0;}
}
.fix-box{
    z-index: 100;position: fixed;bottom:0;padding-right: 20px;left:211px;right: 0px;
    transition: all 0.3s;
    >div{
        background: #fff;border-top: 1px solid #dcdfe6;box-shadow: 0 -5px 12px 0 rgba(0, 0, 0, 0.1);padding-right: 10px;
    }
    .fix-box-btn{width: 32px;height: 32px;cursor: pointer;display: flex;justify-content: center;align-items: center;font-size: 16px}
}
.fix-box.cur{height: 50px}
.fix-box.isCollapse{left: 75px}
.el-form-item .remark-input{max-width: 100%!important;width: 100%}
.message-content{
    color: #333;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .read-icon{display: inline-block;width: 5px;height: 5px;background: red;border-radius: 5px;margin-right: 5px}
}
.message-content.isRead{
    color: #999;
    .read-icon{background: transparent}
}
.total{
    margin-top: 10px;
    table{
        border-collapse:collapse;width: 100%;font-size: 14px;color: #333;
        tr td{line-height: 30px;background: #8f949a;color: #fff;padding: 5px 10px;}
        tr td.title{width: 100px;}
    }
}
.download-file{color: #409EFF;cursor: pointer;display:block}
.menu-icon{width: 16px;height: 16px}
.red-input .el-input__inner{border-color: red!important;}

.button-bar.needfix{
    position: fixed;padding: 10px 0px 10px 10px;right: 20px;left: 211px;background: #fff;z-index: 1000;top: 60px;margin-right: 0!important;
}
.button-bar.isCollapse{left: 75px}
//.main-content{
//    .is-required .el-input__inner{background: #dee85b}
//    .el-input.is-disabled .el-input__inner{background: #ebebeb}
//}
//
//.el-table .warning-row {
//    background: #f0f9eb;
//}
//.el-table .error-row {
//    background: #f56c6c;
//}
//.el-table--small td, .el-table--small th{padding: 0;}
//




.el-autocomplete{width: 100%}
.h-tab{
    overflow: hidden;
    overflow-x:auto;
    box-sizing: border-box;
    height: 41px;
    .h-tab-list{
        white-space: nowrap;
        box-sizing: border-box;
        display: inline-block;
        border-radius: 4px 4px 0 0;
        transition: transform .3s;
        overflow: hidden;
        border: 1px solid #e4e7ed;
        border-bottom: none;
    }
    a{
        cursor: pointer;border-right: 1px solid #e4e7ed;
        float:left;display: inline-block;padding: 0 20px;line-height: 40px;color: #303133;font-size: 14px;font-weight: 500;
        transition: color .3s cubic-bezier(.645,.045,.355,1),padding .3s cubic-bezier(.645,.045,.355,1);
    }
    a.cur{
        color: #409eff;
    }
    a:last-child{border-right: none}
}


.status-bar{
    border: 1px solid #d7dae2;display: flex;min-height: 80px;justify-content: space-between;
    overflow: hidden;padding: 0 0 0 20px;margin: 10px 0 20px;
    .circle{width: 16px;height: 16px;border-radius: 16px;margin-right: 10px}
    >a{background: red;width: 100px;color: #fff;display: flex;align-items: center;justify-content: center}
    .status1{
        color: #FE9400;
        .circle{background: #FE9400}
    }
    .status2{
        color: #1ABC9C;
        .circle{background: #1ABC9C}
    }
    .status3{
        color: #FF0000;
        .circle{background: #FF0000}
    }
    >*{flex-shrink:0}
    span{margin-right: 10px;display: flex;justify-content: center;align-items: center;}
}
.ex-company-name{
    font-size: 14px;color: #606266;line-height: 34px;
}
.el-button--mini{
    padding: 7px 5px;
}
// 表格内编辑样式
.table-nopadding{
    .cell, td{
        padding: 0!important;
    }
    .el-form-item{
        margin-bottom: 0;
    }
    .el-input__inner{
        border-color: transparent;
        padding: 0 5px;
    }
    .el-upload--text{
        width: 100%;
        height: 100%;
        border: none;
        display: block;
    }
    .is-error .el-upload--text{
        border: 1px solid #F56C6C;
    }
}
.sticky{
    div{
        background: #d0d0d0;
    }
    .el-row{
        padding: 5px 0;
    }
}
.el-input-group__append, .el-input-group__prepend{
    padding: 0 5px;
}
.el-table--border td, .el-table--border th{
    border-right: none;
}
.el-table--border{
    border-left: none;
}
.sidebar {
    .el-scrollbar__bar.is-vertical {
        right: 0px;
      }
      .el-scrollbar__view{
          min-height: 100%;
      }
      .el-scrollbar {
        height: 100%;
      }
      &.has-logo {
        .el-scrollbar {
          height: calc(100% - 50px);
        }
      }
    
      .is-horizontal {
        display: none;
      }
    
      a {
        display: inline-block;
        width: 100%;
        overflow: hidden;
      }
    
      .scrollbar-wrapper {
        overflow-x: hidden !important;
      }
}
.preview-container {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0,0,0,.5);
    transition: opacity .3s;
    display: flex;
    justify-content: center;
    align-items: center;
    span{
        margin: 5px
    }
    &:hover{
        opacity: 1;
    }
}

.input-button {
    width: auto;
  .el-input__inner  {
    color: #409EFF;
    width: auto;
  }
}