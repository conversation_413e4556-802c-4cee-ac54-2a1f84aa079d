/*
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-06-07 15:53:58
 */
import axios from 'axios'
import { Loading, Message } from 'element-ui'
export default function exportMethod(url, params, fileName, callback) {
    axios({
        url:url,
        headers:{token:localStorage.getItem('companyToken')},
        params,
        responseType: 'blob'
    }).then((res) => {
        callback()
        let data = res.data;
        let fileReader = new FileReader();
        fileReader.onload = function() {
            try {
                let jsonData = JSON.parse(this.result);
                if(jsonData.code==401){
                    Message.error({
                        message: '请登录后再操作'
                    });
                    window.location.href = "/#/login";
                    localStorage.setItem('companyToken','');
                    return;
                }
            } catch (err) {
                const link = document.createElement('a')
                let blob = new Blob([res.data], {type: 'application/vnd.ms-excel'})
                link.style.display = 'none'
                link.href = URL.createObjectURL(blob)
                // link.download = res.headers['content-disposition'] //下载后文件名
                link.download = fileName+'.xls' //下载的文件名
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            }
        };
        fileReader.readAsText(data);
    }).catch(error => {
        callback()
        Message.error({
            message: '网络连接错误!'
        })
        console.log(error)
    })
}