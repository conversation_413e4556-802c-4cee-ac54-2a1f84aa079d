const status = {
    coStatus: [
        { value: '', label: '请选择' },
        { value: 0, label: '已到期' },
        { value: 1, label: '启用中' },
        { value: 2, label: '已冻结' }
    ],
    productStuats: [
        { value: '', label: '请选择' },
        { value: '0', label: '已停用' },
        { value: '1', label: '已启用' }
    ],
    userStatus: [
        { value: '', label: '请选择' },
        { value: 0, label: '禁用' },
        { value: 1, label: '正常' }
    ],
    sendType: [
        { value: '', label: '请选择' },
        { value: 1, label: '直发' },
        { value: 2, label: '转运' }
    ],
    baoQingGuan: [
        { value: '', label: '请选择' },
        { value: 0, label: '不包清关' },
        { value: 1, label: '包清关' }
    ],
    productType: [
        { value: '', label: '请选择' },
        { value: 1, label: '普货' },
        { value: 2, label: '特货' }
    ],
    // 运单状态 不传或者0时查询全部订单，状态100待发货，200待收货，300待回录，400已收货，500已退回，600问题件，1000已删除，2000已完成，3000已关闭
    orderStatus: [
        { value: '', label: '请选择' },
        { value: 100, label: '待发货' },
        { value: 200, label: '待收货' },
        { value: 300, label: '待回录' },
        { value: 400, label: '已收货' },
        { value: 500, label: '已退回' },
        { value: 600, label: '问题件' },
        { value: 1000, label: '已作废' },
        { value: 2000, label: '已签收' },
        { value: 3000, label: '已结束' }
    ],
    rollback: [
        { value: '', label: '请选择' },
        { value: 200, label: '全部退回' },
        { value: 210, label: '部分退回' }
    ],
    question: [
        { value: 1, label: '转运滞留' },
        { value: 2, label: '海关扣留' },
        { value: 3, label: '找不到收件人' },
        { value: 4, label: '收货少货' },
    ],
    putStatus: [
        { value: 0, label: '待出库' },
        { value: 1, label: '已出库' },
    ],
    supplierType: [
        { value: '', label: '请选择' },
        { value: 1, label: '国内快递' },
        { value: 2, label: '国际快递' },
        { value: 3, label: '物料供应商' },
        { value: 4, label: '其他供应商' }
    ],
    packageType: [
        { value: '', label: '请选择' },
        { value: 1, label: 'BOX' },
        { value: 2, label: 'LTR' },
        { value: 3, label: 'PAG' }
    ],
    /**
     * @name: 公共状态
     * @param 1 待审核 2 已审核
     */

    commonStatus: [
        { value: '', label: '请选择' },
        { value: 1, label: '待审核' },
        { value: 2, label: '已审核' }
    ],
    receivableFeeType: [
        { value: '', label: '请选择' },
        { value: 1, label: '发货扣款' },
        { value: 2, label: '初期' }
    ],
    oneOpinion: [
        { value: 1, label: '过程登记' },
        { value: 2, label: '确认遗失' },
        { value: 3, label: '找到了' }
    ],
    twoOpinion: [
        { value: 1, label: '协助清关' },
        { value: 2, label: '结束订单' }
    ],
    threeOpinion: [
        { value: 1, label: '等待处理' },
        { value: 2, label: '重派' },
        { value: 3, label: '销毁处理' },
        { value: 4, label: '国际退回' }
    ],
    fourOpinion: [
        { value: 1, label: '处理记录' },
        { value: 2, label: '确认赔偿' }
    ],
    backType: [
        { value: 1, label: '全部退回' },
        { value: 2, label: '部分退回' }
    ],
    orderSettleStatus: [
        { value: 1, label: '待审核' },
        { value: 2, label: '已结束' },
        { value: 3, label: '已签收' },
        { value: 4, label: '已记账' },
        { value: 5, label: '全部' },
        { value: 6, label: '待锁定' },
        { value: 7, label: '已审核' }
    ],
    orderPaymentMode: [
        { value: 1, label: '微信支付' },
        { value: 2, label: '支付宝支付' },
        { value: 3, label: '现金支付' },
        { value: 4, label: '银行卡支付' },
    ],
    jfType: [
        { value: 1, label: '默认' },
        { value: 2, label: '半抛' },
        { value: 3, label: '1/3抛' },
        { value: 4, label: '小包计费' },
        { value: 5, label: '海运计费' }
    ],
    outType: [
        { value: 1, label: '普通扫描出库' },
        { value: 2, label: '合并扫描出库' }
    ]

}
const filters = {
    formatters: function (val, format) {
        if (typeof (format) === 'function') {
            return format(val)
        } else return val
    },
    coStatus: function (val) {
        let str = '', len = status.coStatus.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.coStatus[i].value === val) {
                    str = status.coStatus[i].label;
                    break;
                }
            }
        }
        return str
    },
    productStuats: function (val) {
        let str = '', len = status.productStuats.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.productStuats[i].value === val) {
                    str = status.productStuats[i].label;
                    break;
                }
            }
        }
        return str
    },
    userStatus: function (val) {
        let str = '', len = status.userStatus.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.userStatus[i].value === val) {
                    str = filterLabel(status.userStatus[i].label);
                    break;
                }
            }
        }
        return str
    },
    sendType: function (val) {
        let str = '', len = status.sendType.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.sendType[i].value === val) {
                    str = status.sendType[i].label;
                    break;
                }
            }
        }
        return str
    },
    baoQingGuan: function (val) {
        let str = '', len = status.baoQingGuan.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.baoQingGuan[i].value === val) {
                    str = status.baoQingGuan[i].label;
                    break;
                }
            }
        }
        return str
    },
    productType: function (val) {
        let str = '', len = status.productType.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.productType[i].value === val) {
                    str = status.productType[i].label;
                    break;
                }
            }
        }
        return str
    },
    orderStatus: function (val) {
        let str = '', len = status.orderStatus.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.orderStatus[i].value === val) {
                    str = status.orderStatus[i].label;
                    break;
                }
            }
        }
        return str
    },
    rollback: function (val) {
        let str = '', len = status.rollback.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.rollback[i].value === val) {
                    str = status.rollback[i].label;
                    break;
                }
            }
        }
        return str
    },
    question: function (val) {
        let str = '', len = status.question.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.question[i].value === val) {
                    str = status.question[i].label;
                    break;
                }
            }
        }
        return str
    },
    putStatus: function (val) {
        let str = '', len = status.putStatus.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.putStatus[i].value === val) {
                    str = status.putStatus[i].label;
                    break;
                }
            }
        }
        return str
    },

    supplierType: function (val) {
        let str = '', len = status.supplierType.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.supplierType[i].value === val) {
                    str = status.supplierType[i].label;
                    break;
                }
            }
        }
        return str
    },

    packageType: function (val) {
        let str = '', len = status.packageType.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.packageType[i].value === val) {
                    str = status.packageType[i].label;
                    break;
                }
            }
        }
        return str
    },

    commonStatus: function (val) {
        let str = '', len = status.commonStatus.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.commonStatus[i].value === val) {
                    str = status.commonStatus[i].label;
                    break;
                }
            }
        }
        return str
    },

    receivableFeeType: function (val) {
        let str = '', len = status.receivableFeeType.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.receivableFeeType[i].value === val) {
                    str = status.receivableFeeType[i].label;
                    break;
                }
            }
        }
        return str
    },

    orderPaymentMode: function (val) {
        let str = '', len = status.orderPaymentMode.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.orderPaymentMode[i].value === val) {
                    str = status.orderPaymentMode[i].label;
                    break;
                }
            }
        }
        return str
    },

    jfType: function (val) {
        let str = '', len = status.jfType.length;
        if (val != undefined) {
            for (let i = 0; i < len; i++) {
                if (status.jfType[i].value === val) {
                    str = status.jfType[i].label;
                    break;
                }
            }
        }
        return str
    },

    filterLabel: function (val) {
        if (!val) return
        let str = val.indexOf('-') > -1 ? val.split('-')[1] : val;
        return str
    },
    filterPrintDate: function (val) {
        if (!val) return
        let m = new Date(val).toDateString().split(" ")[1];
        let arr = val.split("-");
        let str = arr[0].substring(2) + '-' + m + '-' + arr[2];
        return str
    }
}
const filterLabel = function (val) {
    if (!val) return
    let str = val.indexOf('-') > -1 ? val.split('-')[1] : val;
    return str
}
export {
    status,
    filters
}

