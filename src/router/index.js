import Vue from 'vue';
import Router from 'vue-router';

Vue.use(Router);


export default new Router({

    routes: [
        {
            path: '/login',
            component: resolve => require(['../components/page/public/Login.vue'], resolve)
        },
        {
            path: '/',
            component: resolve => require(['../components/common/Home.vue'], resolve),
            children: [
                {
                    path: '/',
                    redirect: '/home/<USER>/list',

                },

                {
                    path: '/home/<USER>/list',
                    component: resolve => require(['../components/page/order/List.vue'], resolve),
                    meta: ['首页', '运单管理', '全部运单']
                },
                {
                    path: '/home/<USER>/edit',
                    component: resolve => require(['../components/page/order/edit/Index.vue'], resolve),
                    meta: ['首页', '运单管理', '编辑运单']
                },
                {
                    path: '/home/<USER>/detail',
                    component: resolve => require(['../components/page/order/Detail.vue'], resolve),
                    meta: ['首页', '运单管理', '运单详情']
                },
                {
                    path: '/home/<USER>/recall',
                    component: resolve => require(['../components/page/order/Recall.vue'], resolve),
                    meta: ['首页', '运单管理', '转关单回录']
                },
                {
                    path: '/home/<USER>/invoice',
                    component: resolve => require(['../components/page/order/Invoice.vue'], resolve),
                    meta: ['首页', '运单管理', '发票打印']
                },


                {
                    path: '/home/<USER>/orderSettlement',
                    component: resolve => require(['../components/page/finance/orderSettlement/List.vue'], resolve),
                    meta: ['首页', '财务管理', '订单结算']
                },
                {
                    path: '/home/<USER>/order/settle',
                    component: resolve => require(['../components/page/finance/orderSettlement/order/Index.vue'], resolve),
                    meta: ['首页', '财务管理', '订单结算']
                },
                {
                    path: '/home/<USER>/monthCost',
                    component: resolve => require(['../components/page/finance/MonthCost.vue'], resolve),
                    meta: ['首页', '财务管理', '月度经营成本']
                },
                {
                    path: '/home/<USER>/monthCost/edit',
                    component: resolve => require(['../components/page/finance/MonthCost-edit.vue'], resolve),
                    meta: ['首页', '财务管理', '月度经营成本编辑']
                },
                {
                    path: '/home/<USER>/monthCost/detail',
                    component: resolve => require(['../components/page/finance/MonthCost-detail.vue'], resolve),
                    meta: ['首页', '财务管理', '月度经营成本详情']
                },
                {
                    path: '/home/<USER>/credit',
                    component: resolve => require(['../components/page/finance/Credit.vue'], resolve),
                    meta: ['首页', '财务管理', '业务代理信用额度']
                },
                {
                    path: '/home/<USER>/credit/edit',
                    component: resolve => require(['../components/page/finance/Credit-edit.vue'], resolve),
                    meta: ['首页', '财务管理', '业务代理信用额度编辑']
                },
                {
                    path: '/home/<USER>/commission',
                    component: resolve => require(['../components/page/finance/Commission.vue'], resolve),
                    meta: ['首页', '财务管理', '业务代理提成发放登记']
                },
                {
                    path: '/home/<USER>/commission/edit',
                    component: resolve => require(['../components/page/finance/Commission-edit.vue'], resolve),
                    meta: ['首页', '财务管理', '业务代理提成发放登记编辑']
                },
                {
                    path: '/home/<USER>/receivable',
                    component: resolve => require(['../components/page/finance/Receivable.vue'], resolve),
                    meta: ['首页', '财务管理', '业务代理应收单']
                },
                {
                    path: '/home/<USER>/receivable/edit',
                    component: resolve => require(['../components/page/finance/Receivable-edit.vue'], resolve),
                    meta: ['首页', '财务管理', '业务代理应收单编辑']
                },
                {
                    path: '/home/<USER>/receivable/detail',
                    component: resolve => require(['../components/page/finance/Receivable-detail.vue'], resolve),
                    meta: ['首页', '财务管理', '业务代理应收单详情']
                },
                {
                    path: '/home/<USER>/receive',
                    component: resolve => require(['../components/page/finance/Receive.vue'], resolve),
                    meta: ['首页', '财务管理', '业务代理收款单']
                },
                {
                    path: '/home/<USER>/receive/edit',
                    component: resolve => require(['../components/page/finance/Receive-edit.vue'], resolve),
                    meta: ['首页', '财务管理', '业务代理收款单编辑']
                },
                {
                    path: '/home/<USER>/suplierReceivable',
                    component: resolve => require(['../components/page/finance/SuplierReceivable.vue'], resolve),
                    meta: ['首页', '财务管理', '供应商应付单']
                },
                {
                    path: '/home/<USER>/suplierReceivable/edit',
                    component: resolve => require(['../components/page/finance/SuplierReceivable-edit.vue'], resolve),
                    meta: ['首页', '财务管理', '供应商应付单编辑']
                },
                {
                    path: '/home/<USER>/suplierReceive',
                    component: resolve => require(['../components/page/finance/SuplierReceive.vue'], resolve),
                    meta: ['首页', '财务管理', '供应商付款单']
                },
                {
                    path: '/home/<USER>/suplierReceive/edit',
                    component: resolve => require(['../components/page/finance/SuplierReceive-edit.vue'], resolve),
                    meta: ['首页', '财务管理', '供应商付款单编辑']
                },



                {
                    path: '/home/<USER>/order',
                    component: resolve => require(['../components/page/report/Order.vue'], resolve),
                    meta: ['首页', '报表查询', '运单查询']
                },
                {
                    path: '/home/<USER>/freight',
                    component: resolve => require(['../components/page/report/Freight.vue'], resolve),
                    meta: ['首页', '报表查询', '运费查询']
                },
                {
                    path: '/home/<USER>/todayProfit',
                    component: resolve => require(['../components/page/report/TodayProfit.vue'], resolve),
                    meta: ['首页', '报表查询', '当日发货毛利统计']
                },
                {
                    path: '/home/<USER>/orderProfit',
                    component: resolve => require(['../components/page/report/OrderProfit.vue'], resolve),
                    meta: ['首页', '报表查询', '业务代理订单利润表']
                },
                {
                    path: '/home/<USER>/orderSend',
                    component: resolve => require(['../components/page/report/OrderSend.vue'], resolve),
                    meta: ['首页', '报表查询', '业务代理月度订单发货统计']
                },
                {
                    path: '/home/<USER>/orderCompensation',
                    component: resolve => require(['../components/page/report/OrderCompensation.vue'], resolve),
                    meta: ['首页', '报表查询', '业务代理月度订单赔偿统计']
                },
                {
                    path: '/home/<USER>/orderCost',
                    component: resolve => require(['../components/page/report/orderCost.vue'], resolve),
                    meta: ['首页', '报表查询', '业务代理本月提成应发']
                },
                {
                    path: '/home/<USER>/businessIncome',
                    component: resolve => require(['../components/page/report/BusinessIncome.vue'], resolve),
                    meta: ['首页', '报表查询', '业务代理应收汇总']
                },
                {
                    path: '/home/<USER>/orderClient',
                    component: resolve => require(['../components/page/report/OrderClient.vue'], resolve),
                    meta: ['首页', '报表查询', '客户应收明细表']
                },
                {
                    path: '/home/<USER>/businessReconciliation',
                    component: resolve => require(['../components/page/report/BusinessReconciliation.vue'], resolve),
                    meta: ['首页', '报表查询', '业务代理对账单']
                },
                {
                    path: '/home/<USER>/supplierIncome',
                    component: resolve => require(['../components/page/report/SupplierIncome.vue'], resolve),
                    meta: ['首页', '报表查询', '供应商应付汇总']
                },
                {
                    path: '/home/<USER>/supplierReconciliation',
                    component: resolve => require(['../components/page/report/SupplierReconciliation.vue'], resolve),
                    meta: ['首页', '报表查询', '供应商对账单']
                },


                {
                    path: '/home/<USER>/product',
                    component: resolve => require(['../components/page/basic/Product.vue'], resolve),
                    meta: ['首页', '基础设置', '商品维护']
                },
                {
                    path: '/home/<USER>/product/edit',
                    component: resolve => require(['../components/page/basic/Product-edit.vue'], resolve),
                    meta: ['首页', '基础设置', '商品维护']
                },
                {
                    path: '/home/<USER>/jn',
                    component: resolve => require(['../components/page/basic/Jn.vue'], resolve),
                    meta: ['首页', '基础设置', '境内收发货人']
                },
                {
                    path: '/home/<USER>/jn/edit',
                    component: resolve => require(['../components/page/basic/Jn-edit.vue'], resolve),
                    meta: ['首页', '基础设置', '境内收发货人']
                },
                {
                    path: '/home/<USER>/jw',
                    component: resolve => require(['../components/page/basic/Jw.vue'], resolve),
                    meta: ['首页', '基础设置', '境外收发货人']
                },
                {
                    path: '/home/<USER>/jw/edit',
                    component: resolve => require(['../components/page/basic/Jw-edit.vue'], resolve),
                    meta: ['首页', '基础设置', '境外收发货人']
                },
                {
                    path: '/home/<USER>/config',
                    component: resolve => require(['../components/page/basic/Config.vue'], resolve),
                    meta: ['首页', '基础设置', '默认设置']
                },




                {
                    path: '/home/<USER>',
                    redirect: '/home/<USER>/welcome',
                },
                {
                    path: '/home/<USER>/welcome',
                    component: resolve => require(['../components/page/index/Welcome.vue'], resolve),
                    meta: ['首页', '首页', '欢迎页']
                },
                {
                    path: '/home/<USER>/notice',
                    component: resolve => require(['../components/page/index/Notice.vue'], resolve),
                    meta: ['首页', '首页', '系统公告']
                },
                {
                    path: '/home/<USER>/notice/detail',
                    component: resolve => require(['../components/page/index/Notice-detail.vue'], resolve),
                    meta: ['首页', '首页', '系统公告详情']
                },
                {
                    path: '/home/<USER>/message',
                    component: resolve => require(['../components/page/index/Message.vue'], resolve),
                    meta: ['首页', '首页', '系统消息']
                },
                {
                    path: '/home/<USER>/message/detail',
                    component: resolve => require(['../components/page/index/Message-detail.vue'], resolve),
                    meta: ['首页', '首页', '系统消息详情']
                },



                //系统消息与公告
                {
                    path: '/home/<USER>/message',
                    component: resolve => require(['../components/page/system/Message.vue'], resolve),
                    meta: ['首页', '系统', '消息']
                },
                {
                    path: '/home/<USER>/message/detail',
                    component: resolve => require(['../components/page/system/Message-detail.vue'], resolve),
                    meta: ['首页', '系统', '消息详情']
                },



                {
                    path: '/home/<USER>',
                    redirect: '/home/<USER>/authority',
                },
                {
                    path: '/home/<USER>/authority',
                    component: resolve => require(['../components/page/role/Authority.vue'], resolve),
                    meta: ['首页', '角色权限', '角色权限']
                },
                {
                    path: '/home/<USER>/authority/edit',
                    component: resolve => require(['../components/page/role/Authority-edit.vue'], resolve),
                    meta: ['首页', '角色权限', '角色权限']
                },
                {
                    path: '/home/<USER>/user',
                    component: resolve => require(['../components/page/role/User.vue'], resolve),
                    meta: ['首页', '角色权限', '职员列表']
                },
                {
                    path: '/home/<USER>/user/edit',
                    component: resolve => require(['../components/page/role/User-edit.vue'], resolve),
                    meta: ['首页', '角色权限', '角色权限']
                },
                {
                    path: '/home/<USER>/config',
                    component: resolve => require(['../components/page/role/Config.vue'], resolve),
                    meta: ['首页', '角色权限', '参数管理']
                },
                {
                    path: '/home/<USER>/syslog',
                    component: resolve => require(['../components/page/role/Syslog.vue'], resolve),
                    meta: ['首页', '角色权限', '系统日志']
                },
                {
                    path: '/home/<USER>/list',
                    component: resolve => require(['../components/page/sign/List.vue'], resolve),
                    meta: ['首页', '到库签收', '到库签收']
                },
                {
                    path: '/home/<USER>/list',
                    component: resolve => require(['../components/page/output/index.vue'], resolve),
                    meta: ['首页', '发货订单', '发货订单']
                },
                {
                    path: '/home/<USER>/edit',
                    component: resolve => require(['../components/page/output/edit.vue'], resolve),
                    meta: ['首页', '发货订单', '编辑']
                },
                {
                    path: '/home/<USER>/detail',
                    component: resolve => require(['../components/page/output/detail.vue'], resolve),
                    meta: ['首页', '出库订单', '详情']
                },


                {
                    path: '/home/<USER>',
                    redirect: '/home/<USER>/list',
                },
                {
                    path: '/home/<USER>/list',
                    component: resolve => require(['../components/page/trade/List.vue'], resolve),
                    meta: ['首页', '一般贸易', '一般贸易']
                },
                {
                    path: '/home/<USER>/simple',
                    name: 'simple',
                    component: resolve => require(['../components/page/trade/Simple.vue'], resolve),
                    meta: ['首页', '一般贸易', '简易新增']
                },
                {
                    path: '/home/<USER>/expert',
                    name: 'expert',
                    component: resolve => require(['../components/page/trade/Expert.vue'], resolve),
                    meta: ['首页', '一般贸易', '专家新增']
                },
                {
                    path: '/home/<USER>/declared',
                    name: 'declared',
                    component: resolve => require(['../components/page/trade/Declared.vue'], resolve),
                    meta: ['首页', '一般贸易', '待申报']
                },
                {
                    path: '/home/<USER>/confirm',
                    name: 'confirm',
                    component: resolve => require(['../components/page/trade/Confirm.vue'], resolve),
                    meta: ['首页', '一般贸易', '待确认']
                },
                {
                    path: '/home/<USER>/original-detail',
                    component: resolve => require(['../components/page/trade/OriginalDetail.vue'], resolve),
                    meta: ['首页', '一般贸易', '申报前详情']
                },
                {
                    path: '/home/<USER>/detail',
                    component: resolve => require(['../components/page/trade/Detail.vue'], resolve),
                    meta: ['首页', '一般贸易', '申报后详情']
                },
                {
                    path: '*',
                    redirect: '/home/<USER>/list',

                },
            ]
        }
    ],
    mode: "hash"
})
