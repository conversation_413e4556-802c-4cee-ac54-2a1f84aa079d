<template>
    <el-select v-model="cId" :areaType="areaType" :countryName="countryName" :countryId="countryId" filterable placeholder="请选择" @change="handleChange" :filter-method="dataFilter">
        <el-option
            v-for="item in clist"
            :key="item.adcode"
            :label="item.name"
            default-first-option
            :value="item.adcode">
        </el-option>
    </el-select>
</template>

<script>
    export default {
        name: "Country",
        props:{
            countryId:{
                default:''
            },
            countryName:{
                default:''
            },
            areaType:{
                default:'3'
            },
        },
        watch:{
            countryId(newVal, oldVal){
                if(newVal&&(newVal!=oldVal)){
                    if(this.countryId)this.cId=this.countryId;
                    if(this.countryName)this.cName=this.countryName;
                }
            }
        },
        data(){
            return{
                cId:'',
                cName:'',
                clist: [{}],
                list:[
                    {}
                ],
            }
        },
        mounted(){
            this.getList();
            this.cId=this.countryId;
            this.cName=this.countryName;
        },
        methods:{
            getList(){
                this.$axios({url:'/sys/area/list',method:'post',data:{parenCode: 0,areaType:this.areaType}, headers:{closeLoading:true}}).then(res=>{
                    if(res.code==0){
                        this.list = res.areaList;
                        this.clist = res.areaList
                    }
                })
            },
            dataFilter(val) {
                if (val) { //val存在
                    this.clist = this.list.filter((item) => {
                        if(item.name.toLowerCase().indexOf(val.toLowerCase()) > -1) return true
                    })
                } else { //val为空时，还原数组
                    this.clist = this.list;
                }
            },
            handleChange(vId){
                let obj = {};
                obj = this.list.find((item)=>{
                    return item.adcode === vId;
                });
                if(obj){
                    this.cName = obj.name;
                }else{
                    this.cId=''
                    this.cName=''
                }
                this.$emit('change',[this.cId,this.cName]);
            }
        }
    }
</script>

<style scoped>

</style>
