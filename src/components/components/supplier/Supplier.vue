<!--
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-09-02 09:18:02
-->
<template>
    <el-select :disabled="disabled" v-model="selfId" filterable @change="handleChange" :placeholder="placeholder">
    <el-option
      v-for="item in list"
      :key="item.id"
      :label="item.supplierName"
      :value="item.id"
    ></el-option>
  </el-select>
</template>

<script>
    import { GetListByType } from "src/api/supplier";
    export default {
        name: "jn",
        props:{
            id:{
                default:''
            },
            placeholder:{
                default: "请选择供应商",
            },
            supplierType: {
                default:''
            },
            disabled: {
                default: false
            }
        },
        watch: {
            id(newVal, oldVal) {
                if (newVal && newVal != oldVal) {
                    if (this.id) this.selfId = this.id;
                }
            },
            supplierType(newVal, oldVal) {
                if (newVal && newVal != oldVal) {
                    this.getList()
                    this.selfId = ''
                    this.item = {}
                    this.$emit("input", '');
                    this.$emit("change", ['', '', '']);
                }
            },

        },
        data(){
            return{
                selfId:'',
                list:[],
                item:{}
            }
        },
        created(){
            this.getList();
            if (this.id) this.selfId = this.id;
        },
        methods:{
            getList() {
                GetListByType({supplierType: this.supplierType ? this.supplierType : 0}, true).then((res) => {
                    this.list = res.listSupplier;
                });
            },
            handleChange(val) {
                const item = this.list.filter((item) => item.id === val);
                this.item = item[0] || {};
                this.$emit("input", this.selfId);
                this.$emit("change", [this.selfId, this.item.supplierName, this.item]);
            },
        },
    }
</script>

<style scoped>

</style>
