<template>
<!--    <el-autocomplete-->
<!--        class="inline-input"-->
<!--        v-model="cName"-->
<!--        :id="id"-->
<!--        :name="name"-->
<!--        value-key="label"-->
<!--        highlight-first-item-->
<!--        :fetch-suggestions="querySearch"-->
<!--        placeholder="请输入内容"-->
<!--        @select="handleSelect"-->
<!--        @blur="handleBlur"-->
<!--    ></el-autocomplete>-->
    <el-select v-model="cId" :name="name" :id="id" filterable placeholder="请选择" @change="handleChange">
        <el-option
            v-for="item in list"
            :key="item.unitId"
            :label="item.unitId+'-'+item.unitName"
            default-first-option
            :value="item.unitId">
        </el-option>
    </el-select>
</template>

<script>
    export default {
        name: "unit",
        props:{
            id:{
                default:''
            },
            name:{
                default:''
            },
        },
        watch: {
            id(newVal, oldVal){
                if(newVal&&(newVal!=oldVal)){
                    this.cId=this.id;
                    this.cName=this.name;
                }
            }
        },
        data(){
            return{
                cId:'',
                cName:'',
                unwatch:'',
                list:[]
            }
        },
        created(){
            this.cId=this.id;
            this.cName=this.name;
            this.getList()
        },
        methods:{
            getList(){
                this.$axios({url:'/courier/comexproduct/getUnitList',method:'post',data:{"keyword":''}, headers:{closeLoading:true}}).then(res=>{
                    if(res.code==0){
                        this.list = res.unitList;
                    }
                })
            },
            handleChange(vId){
                let obj = {};
                obj = this.list.find((item)=>{
                    return item.unitId === vId;
                });
                if(obj){
                    this.cName = obj.unitName;
                }else{
                    this.cId=''
                    this.cName=''
                }
                this.$emit('change',[this.cId,this.cName]);
            }
        },
    }
</script>

<style scoped>

</style>
