<template>
    <el-autocomplete
        class="inline-input"
        v-model="cName"
        :id="id"
        :name="name"
        hide-loading
        :debounce="1"
        value-key="chName"
        highlight-first-item
        :fetch-suggestions="querySearch"
        placeholder="请输入内容"
        @select="handleSelect"
        @blur="handleBlur"
    ></el-autocomplete>

</template>

<script>
    export default {
        name: "channel",
        props:{
            id:{
                default:''
            },
            name:{
                default:''
            },
        },
        watch: {
            id(newVal, oldVal){
                if(newVal&&(newVal!=oldVal)){
                    this.cId=this.id;
                    if(this.name)this.cName=this.name;
                    this.handleDefault()
                }
            }
        },
        data(){
            return{
                cId:'',
                cName:'',
                list:[]
            }
        },
        created(){
            // this.handleDefault()
        },
        methods:{
            querySearch(q, cb) {
                let params={
                    page:1,
                    limit:50,
                    chName:q,
                };
                this.$axios({url:'/courier/jbrsendchannel/list',method:'get',params:params, headers:{closeLoading:true}}).then(res=>{
                    if(res.code==0){
                        this.list = res.page.list;
                        cb(res.page.list);
                    }
                })
            },
            handleSelect(item) {
                if(item.chId){
                    this.cId = item.chId;
                    this.cName = item.chName;
                }else{
                    this.cId = '';
                    this.cName = '';
                }
                this.$emit('change',[this.cId,this.cName]);
            },
            handleDefault(){
                if(this.cId==''||this.cId==undefined||this.cId==null)return
                this.$axios({url:'/courier/jbrsendchannel/info/'+this.cId,method:'get', headers:{closeLoading:true}}).then(res=>{
                    if(res.code==0){
                        if(res.jbrSendChannel){
                            this.handleSelect(res.jbrSendChannel)
                        }else{
                            this.handleSelect({})
                        }
                    }
                })
            },
            handleBlur(){
                let obj = this.list.filter(item=>{
                    return item.chId==this.cId&&this.cName!='';
                });
                if(obj.length>0){
                    this.handleSelect(obj[0])
                }else{
                    this.handleSelect({})
                }
            }
        },
    }
</script>

<style scoped>

</style>
