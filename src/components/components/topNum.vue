<template>
  <el-select v-model="selfId" filterable placeholder="请选择手机号前缀" @change="handleChange">
    <el-option
      v-for="item in topNum"
      :key="item.id"
      :label="item.topNum+'('+item.countryName+')'"
      :value="item.id"
    ></el-option>
  </el-select>
</template>
<script>
export default {
  props: {
    id: {
      type: [Number, String],
      default: "",
    },
    num: {
      type: [Number, String],
      default: "",
    },
  },
  watch: {
    id(newVal, oldVal) {
      if (newVal && newVal != oldVal) {
        if (this.id) this.selfId = this.id;
        if (this.num) this.selfNum = this.num;
      }
    }
  },
  data() {
    return {
			selfId: '',
			selfNum: '',
      topNum: [],
    };
  },
  created() {
    this.getTopNum();
  },
  methods: {
    getTopNum() {
      this.$axios({
        url: "/sys/topnummobile/list",
        method: "get",
        headers: { closeLoading: true },
      }).then((res) => {
        if (res.code == 0) {
          this.topNum = res.list;
        }
      });
		},
		handleChange(id){
			const arr = this.topNum.filter(item => item.id === id)
			if(arr && arr.length > 0){
				this.selfNum = arr[0].topNum
				this.$emit('input',this.selfId);
				this.$emit('change',[this.selfId, this.selfNum, arr[0]]);
			}else{
				this.$emit('input', '');
				this.$emit('change', ['', '', '']);
			}
		}
  },
};
</script>