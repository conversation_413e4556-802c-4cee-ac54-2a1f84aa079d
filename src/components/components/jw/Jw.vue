<template>
    <el-autocomplete
        @input="cName = cName.toUpperCase()"
        class="inline-input"
        v-model="cName"
        :id="id"
        :name="name"
        hide-loading
        :debounce="1"
        value-key="nickName"
        highlight-first-item
        :fetch-suggestions="querySearch"
        placeholder="请输入内容"
        @select="handleSelect"
        @blur="handleBlur"
    ></el-autocomplete>

</template>

<script>
    export default {
        name: "jw",
        props:{
            id:{
                default:''
            },
            name:{
                default:''
            },
        },
        watch: {
            id(newVal, oldVal){
                // console.log('收发货人',newVal,oldVal)
                if(newVal&&(newVal!=oldVal)){
                    if(this.id)this.cId=this.id;
                    if(this.name)this.cName=this.name;
                    this.handleDefault()
                }
            },
            cName(newVal, oldVal){
                if(newVal&&(newVal!=oldVal)){
                    // this.$emit('change',[this.cId,this.cName.toUpperCase()]);
                }
            }
        },
        data(){
            return{
                cId:'',
                cName:'',
                list:[],
                item:{}
            }
        },
        mounted(){
            if(this.id){
                // console.log('组件创建时',this.id)
                if(this.id)this.cId=this.id;
                if(this.name)this.cName=this.name;
                this.handleDefault()
            }
        },
        methods:{
            querySearch(q, cb) {
                let params={
                    page:1,
                    limit:50,
                    name:q,
                };
                this.$axios({url:'/courier/jbrjwdeliveruser/list',method:'get',params:params, headers:{closeLoading:true}}).then(res=>{
                    if(res.code==0){
                        this.list = res.page.list;
                        cb(res.page.list);
                    }
                })
            },
            handleSelect(item) {
                if(item.did){
                    this.cId = item.did;
                    this.cName = item.nickName.toUpperCase();
                    this.item=item
                }else{
                    this.cId = '';
                    this.cName = '';
                    this.item={}
                }
                this.$emit('input',this.cId);
                this.$emit('change',[this.cId,this.cName,this.item]);
            },
            handleDefault(){
                if(this.cId==''||this.cId==undefined||this.cId==null)return
                this.$axios({url:'/courier/jbrjwdeliveruser/info/'+this.cId,method:'get', headers:{closeLoading:true}}).then(res=>{
                    if(res.code==0){
                        if(res.jbrJwDeliverUser){
                            this.handleSelect(res.jbrJwDeliverUser)
                        }else{
                            this.handleSelect({})
                        }
                    }
                })
            },
            handleBlur(){
                let obj = this.list.filter(item=>{
                    return item.did==this.cId&&this.cName!='';
                });
                if(obj.length>0){
                    this.handleSelect(obj[0])
                }else{
                    this.handleSelect({})
                }
            }
        },
    }
</script>

<style scoped>

</style>
