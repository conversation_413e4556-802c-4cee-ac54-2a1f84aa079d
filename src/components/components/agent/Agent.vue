<!--
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-09-01 11:12:43
-->
<template>
  <el-select v-model="selfId" :multiple="multiple" filterable @change="changeAgent" placeholder="请选择业务代理">
    <el-option
      v-for="item in agents"
      :key="item.businessUserId"
      :label="item.nickName + '(' + item.username + ')'"
      :value="item.businessUserId"
    ></el-option>
  </el-select>
</template>

<script>
import { GetBusinessAgent } from "src/api/finance/credit";
export default {
  name: "agent",
  props: {
    id: {
      default: "",
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    id(newVal, oldVal) {
      // console.log('收发货人',newVal,oldVal)
      if (newVal && newVal != oldVal) {
        if (this.id) this.selfId = this.id;
      }
    },
  },
  data() {
    return {
      selfId: "",
      agents: [],
      item: {},
    };
  },
  created() {
    this.getAgent();
  },
  mounted() {
    if (this.id) this.selfId = this.id;
  },
  methods: {
    getAgent() {
      GetBusinessAgent({
        nickName: "",
        username: "",
      }).then((res) => {
        this.agents = res.listBusiness;
      });
    },
    changeAgent(val) {
      if (this.multiple) {
        const item = this.agents.filter((item) => val.includes(item.businessUserId) );
        const name = item.map(item => item.nickName)
        this.$emit("input", this.selfId);
        this.$emit("change", [this.selfId, name.toString()]);
      } else {
        const item = this.agents.filter((item) => item.businessUserId === val);
        this.item = item[0] || {};
        this.$emit("input", this.selfId);
        this.$emit("change", [this.selfId, this.item.nickName, this.item]);
      }
    },
  },
};
</script>

<style scoped>
</style>
