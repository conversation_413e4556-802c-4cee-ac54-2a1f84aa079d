<!--
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-09-01 11:12:43
-->
<template>
  <el-select v-model="selfId" filterable @change="handleChange" :placeholder="placeholder">
    <el-option
      v-for="item in list"
      :key="item.id"
      :label="item.payName"
      :value="item.id"
    ></el-option>
  </el-select>
</template>

<script>
import { GetAllPayment } from "src/api/payment";
export default {
  name: "payment",
  props: {
    id: {
      default: "",
    },
    type: {
      default: "",
    },
    placeholder:{
      default: "请选择",
    }
  },
  watch: {
    id(newVal, oldVal) {
      // console.log('收发货人',newVal,oldVal)
      if (newVal && newVal != oldVal) {
        if (this.id) this.selfId = this.id;
      }
    },
  },
  data() {
    return {
      selfId: "",
      list: [],
      item: {},
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    if (this.id) this.selfId = this.id;
  },
  methods: {
    getList() {
      GetAllPayment({
        payType: this.type
      }).then((res) => {
        this.list = res.listPayType;
      });
    },
    handleChange(val) {
      const item = this.list.filter((item) => item.id === val);
      this.item = item[0] || {};
      this.$emit("input", this.selfId);
      this.$emit("change", [this.selfId, this.item.payName, this.item]);
    },
  },
};
</script>

<style scoped>
</style>
