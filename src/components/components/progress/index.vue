<!--
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-12-06 13:22:32
-->
<template>
	<el-dialog class="progress-dialog" fullscreen :close-on-click-modal="false" :close-on-press-escape="false" top="0"
		:visible.sync="dislogVisible">
		<el-progress type="circle" :percentage="percent"></el-progress>
	</el-dialog>
</template>

<script>
	export default {
		data() {
			return {
				dislogVisible: false,
				percent: 0,
				timer: null
			};
		},
		created() {

		},
		mounted() {

		},
		methods: {
			init() {
				this.dislogVisible = true
				this.percent = 0
				this.handlePercent()
			},
			handlePercent() {
				if (this.percent >= 100) return
				if (this.percent < 70) {
					clearTimeout(this.timer)
					this.timer = setTimeout(() => {
						this.percent ++
						this.handlePercent()
					}, 300)
				} else if (this.percent < 90) {
					clearTimeout(this.timer)
					this.timer = setTimeout(() => {
						this.percent ++
						this.handlePercent()
					}, 1000)
				} else if (this.percent < 99) {
					clearTimeout(this.timer)
					this.timer = setTimeout(() => {
						this.percent ++
						this.handlePercent()
					}, 5000)
				}
			},
			close() {
				clearTimeout(this.timer)
				this.percent = 100
				setTimeout(() => {
					this.$nextTick(() => {
						this.dislogVisible = false
					})
				}, 500)		
			}
		}
	};
</script>
<style lang="less">
	.progress-dialog {
		.el-dialog {
			background: transparent;
			.el-dialog__body {
				height: 100%;
				padding: 0;
				position: relative;
				.el-progress {
					position: absolute;
					left: 50%;
					top: 50%;
					margin-left: -63px;
					margin-top: -80px;
					.el-progress__text {
						color: #fff;
					}
				}
			}
		}
	}
</style>