<template>
    <div class="block-section">
        <div class="button-bar cl white-bg" style="margin-right: 0;padding-bottom: 0;margin-bottom: 0;padding-right: 0">
<!--            <el-button class="fl" @click="download">下载核对单</el-button>-->
<!--            <el-button class="fl" type="primary" @click="frozen" v-if="ruleForm.orderId">{{ruleForm.frozen==1?'解冻':'冻结'}}</el-button>-->

            <el-button class="fr" @click="back()">返回</el-button>
            <el-button class="fr" type="primary" @click="confirm('1')" v-if="ruleForm.orderId&&ruleForm.orderStatus==500&&ruleForm.myConfirm!=1">确认无误</el-button>
            <el-button class="fr" type="primary" @click="confirm('2')" v-if="ruleForm.orderId&&ruleForm.orderStatus==500&&ruleForm.myConfirm!=1">有误退回</el-button>
        </div>
        <div class="block-section-title cl">
            <i class="el-icon-remove-outline"></i>
            核对单信息
            <span class="confirm-status" :class="{'cur':ruleForm.myConfirm==1}">外贸公司{{ruleForm.myConfirm==1?'已确认':'未确认'}}</span>
            <span class="confirm-status" :class="{'cur':ruleForm.hyConfirm==1}">货运公司{{ruleForm.hyConfirm==1?'已确认':'未确认'}}</span>
            <span class="confirm-status fr mr_1" style="margin-top: 5px;" :class="{'cur':ruleForm.myConfirm==1}">{{ruleForm.myConfirm==1?'已确认':'待确认'}}</span>
        </div>
        <template v-for="item in page">
            <div class="stl_02" :ref="'print'+item">
                <div class="stl_03" v-if="item==1">
                    <img src="static/pdf/hwbgd/bg1.jpg" alt="" style="position:absolute; width:70.1667em; height:46.5625em;">
                </div>
                <div class="stl_03" v-else>
                    <img src="static/pdf/hwbgd/bg2.jpg" alt="" style="position:absolute; width:70.1667em; height:46.5625em;">
                </div>
                <div class="view">
                    <div class="stl_05 stl_06">
                        <div class="stl_01" style="top:2.1702em; left:47.8267em;"><span class="stl_07 stl_08 stl_09"><!--*{{ruleForm.declareNo}}*--> &nbsp;</span></div>
                        <div class="stl_01" style="top:3.0534em; left:22.9692em;"><span class="stl_10 stl_11 stl_09">中华人民共和国海关出口货物报关单 &nbsp;</span></div>
                        <div class="stl_01" style="top:6.1763em; left:3.3125em;"><span class="stl_12 stl_11 stl_09">预录入编号：</span><span class="stl_13 stl_11 stl_09"> &nbsp;</span></div>
                        <div class="stl_01" style="top:6.1763em; left:21.4375em;"><span class="stl_12 stl_11 stl_09">海关编号：</span><span class="stl_13 stl_11 stl_09"> {{ruleForm.declareNo}}&nbsp;</span></div>
                        <div class="stl_01" style="top:6.2888em; left:33.9375em;"><span class="stl_13 stl_11 stl_09">({{ruleForm.declareCustomsName|filterLabel}}) &nbsp;</span></div>



                        <template v-if="item==1">
                            <div class="stl_01" style="top:6.2888em; left:62.6875em;"><span class="stl_12 stl_11 stl_09">页码/页数:</span><span class="stl_14 stl_11 stl_09">1/{{page}} &nbsp;</span></div>
                            <div class="stl_01" style="top:7.4888em; left:3.375em;"><span class="stl_12 stl_11 stl_09" style="word-spacing:-0.1em;">境内发货人 </span><span class="stl_14 stl_11 stl_09" style="word-spacing:-0.0833em;">({{ruleForm.abCustomerCode}}) &nbsp;</span></div>
                            <div class="stl_01" style="top:8.4263em; left:3.375em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.abName}} &nbsp;</span></div>
                            <div class="stl_01" style="top:7.4888em; left:21.5625em;"><span class="stl_12 stl_11 stl_09" style="word-spacing:-0.1em;">出境关别 </span><span class="stl_14 stl_11 stl_09" style="word-spacing:-0.0833em;">({{ruleForm.deparTypeId}}) &nbsp;</span></div>
                            <div class="stl_01" style="top:8.4263em; left:21.5625em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.deparTypeName|filterLabel}} &nbsp;</span></div>
                            <div class="stl_01" style="top:7.4837em; left:32.25em;"><span class="stl_12 stl_11 stl_09">出口日期 &nbsp;</span></div>
                            <div class="stl_01" style="top:7.4837em; left:43.3125em;"><span class="stl_12 stl_11 stl_09">申报日期 &nbsp;</span></div>
                            <div class="stl_01" style="top:7.4837em; left:53.375em;"><span class="stl_12 stl_11 stl_09">备案号 &nbsp;</span></div>
                            <div class="stl_01" style="top:8.4212em; left:53.375em;"><span class="stl_12 stl_11 stl_09">{{ruleForm.putRecordsNo}} &nbsp;</span></div>
                            <div class="stl_01" style="top:8.4263em; left:43.3125em;"><span class="stl_13 stl_11 stl_09"> &nbsp;</span></div>
                            <div class="stl_01" style="top:9.4212em; left:43.3125em;"><span class="stl_12 stl_11 stl_09">提运单号 &nbsp;</span></div>
                            <div class="stl_01" style="top:9.5337em; left:3.375em;"><span class="stl_12 stl_11 stl_09">境外收货人 &nbsp;</span></div>
                            <div class="stl_01" style="top:9.4263em; left:21.5625em;"><span class="stl_12 stl_11 stl_09" style="word-spacing:-0.1em;">运输方式 </span><span class="stl_14 stl_11 stl_09" style="word-spacing:-0.0833em;">({{ruleForm.modeTranId}}) &nbsp;</span></div>
                            <div class="stl_01" style="top:10.3638em; left:21.5625em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.modeTranName|filterLabel}} &nbsp;</span></div>
                            <div class="stl_01" style="top:9.4212em; left:32.25em;"><span class="stl_12 stl_11 stl_09">运输工具名称及航次号 &nbsp;</span></div>
                            <div class="stl_01" style="top:10.3638em; left:3.375em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.overseasCons}} &nbsp;</span></div>
                            <div class="stl_01" style="top:10.3638em; left:32.25em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.boatName+'/'+ruleForm.voyage}} &nbsp;</span></div>
                            <div class="stl_01" style="top:11.3638em; left:32.25em;"><span class="stl_12 stl_11 stl_09" style="word-spacing:-0.1em;">征税性质 </span><span class="stl_14 stl_11 stl_09" style="word-spacing:-0.0833em;">({{ruleForm.exemptionId}}) &nbsp;</span></div>
                            <div class="stl_01" style="top:12.3013em; left:32.25em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.exemption}} &nbsp;</span></div>
                            <div class="stl_01" style="top:10.3638em; left:43.3125em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.billNo}} &nbsp;</span></div>
                            <div class="stl_01" style="top:11.3587em; left:43.3125em;"><span class="stl_12 stl_11 stl_09">许可证号 &nbsp;</span></div>
                            <div class="stl_01" style="top:12.2962em; left:43.3125em;"><span class="stl_12 stl_11 stl_09">{{ruleForm.licenseKey}} &nbsp;</span></div>
                            <div class="stl_01" style="top:11.3638em; left:3.375em;"><span class="stl_12 stl_11 stl_09" style="word-spacing:-0.1em;">生产销售单位 </span><span class="stl_14 stl_11 stl_09" style="word-spacing:-0.0833em;">({{ruleForm.spCustomerCode}}) &nbsp;</span></div>
                            <div class="stl_01" style="top:12.3013em; left:3.375em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.spName}} &nbsp;</span></div>
                            <div class="stl_01" style="top:13.2962em; left:3.375em;"><span class="stl_12 stl_11 stl_09">合同协议号 &nbsp;</span></div>
                            <div class="stl_01" style="top:14.2337em; left:3.375em;"><span class="stl_12 stl_11 stl_09">{{ruleForm.contractNo}} &nbsp;</span></div>
                            <div class="stl_01" style="top:11.3638em; left:21.5625em;"><span class="stl_12 stl_11 stl_09" style="word-spacing:-0.1em;">监管方式 </span><span class="stl_14 stl_11 stl_09" style="word-spacing:-0.0833em;">({{ruleForm.supId}}) &nbsp;</span></div>
                            <div class="stl_01" style="top:12.3013em; left:21.5625em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.supMode}} &nbsp;</span></div>
                            <div class="stl_01" style="top:13.3013em; left:21.5625em;"><span class="stl_12 stl_11 stl_09" style="word-spacing:-0.1em;">贸易国（地区） </span><span class="stl_14 stl_11 stl_09" style="word-spacing:-0.0833em;">({{ruleForm.myStateId}}) &nbsp;</span></div>
                            <div class="stl_01" style="top:14.2388em; left:21.5625em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.myStateName}} &nbsp;</span></div>
                            <div class="stl_01" style="top:13.3013em; left:32.25em;"><span class="stl_12 stl_11 stl_09" style="word-spacing:-0.1em;">运抵国（地区） </span><span class="stl_14 stl_11 stl_09" style="word-spacing:-0.0833em;">({{ruleForm.destinCountryId}}) &nbsp;</span></div>
                            <div class="stl_01" style="top:14.2388em; left:32.25em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.destinCountryName}} &nbsp;</span></div>
                            <div class="stl_01" style="top:15.2337em; left:32.25em;"><span class="stl_12 stl_11 stl_09">净重(千克) &nbsp;</span></div>
                            <div class="stl_01" style="top:13.3013em; left:43.3125em;"><span class="stl_12 stl_11 stl_09" style="word-spacing:-0.1em;">指运港 </span><span class="stl_14 stl_11 stl_09" style="word-spacing:-0.0833em;">({{ruleForm.portDestinId}}) &nbsp;</span></div>
                            <div class="stl_01" style="top:14.2388em; left:43.3125em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.portDestin}} &nbsp;</span></div>
                            <div class="stl_01" style="top:15.1263em; left:38.875em;"><span class="stl_12 stl_11 stl_09" style="word-spacing:-0.1em;">成交方式 </span><span class="stl_14 stl_11 stl_09" style="word-spacing:-0.0833em;">({{ruleForm.cjTypeId}})</span><span class="stl_12 stl_11 stl_09" style="word-spacing:0.3em;"> 运费 &nbsp;</span></div>
                            <div class="stl_01" style="top:16.1763em; left:38.875em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.cjTypeName|filterLabel}} &nbsp;</span></div>
                            <div class="stl_01" style="top:16.1763em; left:43.3125em;"><span class="stl_12 stl_11 stl_09" style="word-spacing:0.3em;"> <!--运费值--> &nbsp;</span></div>
                            <div class="stl_01" style="top:13.3013em; left:53.375em;"><span class="stl_12 stl_11 stl_09" style="word-spacing:-0.1em;">离境口岸 </span><span class="stl_14 stl_11 stl_09" style="word-spacing:-0.0833em;">({{ruleForm.portDepartureId}}) &nbsp;</span></div>
                            <div class="stl_01" style="top:14.2388em; left:53.375em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.portDeparture|filterLabel}} &nbsp;</span></div>
                            <div class="stl_01" style="top:15.2388em; left:3.375em;"><span class="stl_12 stl_11 stl_09" style="word-spacing:-0.1em;">包装种类 </span><span class="stl_14 stl_11 stl_09" style="word-spacing:-0.0833em;">({{ruleForm.packageTypeId}}) &nbsp;</span></div>
                            <div class="stl_01" style="top:15.2337em; left:21.5625em;"><span class="stl_12 stl_11 stl_09">件数 &nbsp;</span></div>
                            <div class="stl_01" style="top:15.2337em; left:25.375em;"><span class="stl_12 stl_11 stl_09">毛重(千克) &nbsp;</span></div>
                            <div class="stl_01" style="top:15.2337em; left:51em;"><span class="stl_12 stl_11 stl_09">保费 &nbsp;</span></div>
                            <div class="stl_01" style="top:16.1712em; left:51em;"><span class="stl_12 stl_11 stl_09"><!--保费值--> &nbsp;</span></div>
                            <div class="stl_01" style="top:15.2337em; left:58.6875em;"><span class="stl_12 stl_11 stl_09">杂费 &nbsp;</span></div>
                            <div class="stl_01" style="top:16.1763em; left:3.375em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.packageTypeName}} &nbsp;</span></div>
                            <div class="stl_01" style="top:17.1712em; left:3.375em;"><span class="stl_12 stl_11 stl_09">随附单证及编号 &nbsp;</span></div>
                            <div class="stl_01" style="top:16.1763em; left:21.5625em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.downHuiSumNum}} &nbsp;</span></div>
                            <div class="stl_01" style="top:16.1763em; left:25.375em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.downHuiRoughWeight}} &nbsp;</span></div>
                            <div class="stl_01" style="top:16.1763em; left:32.25em;"><span class="stl_13 stl_11 stl_09">{{ruleForm.downSuttle}} &nbsp;</span></div>
                            <div class="stl_01" style="top:18.1138em; left:3.375em;"><span class="stl_13 stl_11 stl_09">随附单证2:代理报关委托协议（纸质）&nbsp;</span></div>
                            <div class="stl_01" style="top:19.2212em; left:3.375em;"><span class="stl_15 stl_11 stl_09">标记唛码及备注 &nbsp;</span></div>
                            <div class="stl_01" style="top:20.0513em; left:3.375em;"><span class="stl_13 stl_11 stl_09" style="word-spacing:normal;">备注:{{ruleForm.remark}}</span></div>
                            <div class="stl_01" style="top:22.9212em; left:3.4592em;"><span class="stl_15 stl_11 stl_09">项号 &nbsp;</span></div>
                            <div class="stl_01" style="top:22.9212em; left:5.7125em;"><span class="stl_15 stl_11 stl_09">商品编号 &nbsp;</span></div>
                            <div class="stl_01" style="top:22.9212em; left:14.5408em;"><span class="stl_15 stl_11 stl_09">商品名称及规格型号 &nbsp;</span></div>
                            <div class="stl_01" style="top:22.9212em; left:28.5783em;"><span class="stl_15 stl_11 stl_09">数量及单位 &nbsp;</span></div>
                            <div class="stl_01" style="top:22.9212em; left:35.7842em;"><span class="stl_15 stl_11 stl_09">单价/总价/币制 &nbsp;</span></div>
                            <div class="stl_01" style="top:22.9212em; left:42.4908em;"><span class="stl_15 stl_11 stl_09">原产国(地区) &nbsp;</span></div>
                            <div class="stl_01" style="top:22.9212em; left:47.9408em;"><span class="stl_15 stl_11 stl_09">最终目的国(地区) &nbsp;</span></div>
                            <div class="stl_01" style="top:22.9212em; left:56.8683em;"><span class="stl_15 stl_11 stl_09">境内货源地 &nbsp;</span></div>
                            <div class="stl_01" style="top:22.9212em; left:64.2em;"><span class="stl_15 stl_11 stl_09">征免 &nbsp;</span></div>
                            <div style="position: absolute;top:24em;left:3.125em;">
                                <ul class="product-list" v-for="(product,index) in ruleForm.productDetail.slice(0,6)">
                                    <li class="item1">
                                        <span class="stl_13 stl_11 stl_09">{{index+1}}</span>
                                        <span class="stl_13 stl_11 stl_09">(0)</span>
                                    </li>
                                    <li class="item2">
                                        <span class="stl_13 stl_11 stl_09">{{product.hsCode}}</span>
                                    </li>
                                    <li class="item3">
                                        <span class="stl_13 stl_11 stl_09">{{product.productName}}</span>
                                        <span class="stl_13 stl_11 stl_09">{{product.property|filterProperty}}</span>
                                    </li>
                                    <li class="item4">
                                        <span class="stl_13 stl_11 stl_09">{{product.volumeTrade}}{{product.cjUnitId|filterCjUnit|filterLabel}}</span>
                                        <span class="stl_13 stl_11 stl_09"></span>
                                        <span class="stl_13 stl_11 stl_09">{{product.volumeTrade}}{{product.cjUnitId|filterCjUnit|filterLabel}}</span>
                                    </li>
                                    <li class="item5">
                                        <span class="stl_13 stl_11 stl_09">{{product.unitPrice}}</span>
                                        <span class="stl_13 stl_11 stl_09">{{product.totalPrices}}</span>
                                        <span class="stl_13 stl_11 stl_09">{{product.currencyId|moneyUnit}}</span>
                                    </li>
                                    <li class="item6">
                                        <span class="stl_13 stl_11 stl_09">中国</span>
                                        <span class="stl_13 stl_11 stl_09">(CHN)</span>
                                    </li>
                                    <li class="item7">
                                        <span class="stl_13 stl_11 stl_09">{{ruleForm.destinCountryName}}</span>
                                        <span class="stl_13 stl_11 stl_09">({{ruleForm.destinCountryId}})</span>
                                    </li>
                                    <li class="item8">
                                        <span class="stl_13 stl_11 stl_09">({{product.churProAddId}}){{product.churProAddId|filterChurProAdd|filterLabel}} 照章征税</span>
                                        <span class="stl_13 stl_11 stl_09">(1)</span>
                                    </li>
                                </ul>
                            </div>

                            <div class="stl_01" style="top:40.6388em; left:9.1033em;"><span class="stl_12 stl_11 stl_09">特殊关系确认:</span><span class="stl_13 stl_11 stl_09">否 &nbsp;</span></div>
                            <div class="stl_01" style="top:40.6388em; left:24.3717em;"><span class="stl_12 stl_11 stl_09">价格影响确认:</span><span class="stl_13 stl_11 stl_09">否 &nbsp;</span></div>
                            <div class="stl_01" style="top:40.6388em; left:38.3908em;"><span class="stl_12 stl_11 stl_09">支付特许权使用费确认:</span><span class="stl_13 stl_11 stl_09">否 &nbsp;</span></div>
                            <div class="stl_01" style="top:40.6388em; left:55.5342em;"><span class="stl_12 stl_11 stl_09">自报自缴:</span><span class="stl_13 stl_11 stl_09">否 &nbsp;</span></div>
                            <div class="stl_01" style="top:42.7212em; left:3.75em;"><span class="stl_12 stl_11 stl_09">报关人员 &nbsp;</span></div>
                            <div class="stl_01" style="top:42.6138em; left:11.25em;"><span class="stl_12 stl_11 stl_09">报关人员证号</span><span class="stl_13 stl_11 stl_09"><!--报关人员证号--> &nbsp;</span></div>
                            <div class="stl_01" style="top:42.7212em; left:21.875em;"><span class="stl_12 stl_11 stl_09">电话 &nbsp;</span></div>
                            <div class="stl_01" style="top:42.6087em; left:31.25em;"><span class="stl_12 stl_11 stl_09">兹申明对以上内容承担如实申报、依法纳税之法律责任 &nbsp;</span></div>
                            <div class="stl_01" style="top:44.5462em; left:41.25em;"><span class="stl_12 stl_11 stl_09">申报单位（签章） &nbsp;</span></div>
                            <div class="stl_01" style="top:42.6087em; left:47.5625em;"><span class="stl_12 stl_11 stl_09">海关批注及签章 &nbsp;</span></div>
                            <div class="stl_01" style="top:44.6888em; left:3.75em;"><span class="stl_12 stl_11 stl_09">申报单位 </span><span class="stl_13 stl_11 stl_09">(91330782MA2DFCD18G)义乌博达报关代理有限公司 &nbsp;</span></div>
                        </template>
                        <template v-else>
                            <div class="stl_01" style="top:6.2888em; left:62.6875em;"><span class="stl_12 stl_11 stl_09">页码/页数:</span><span class="stl_14 stl_11 stl_09">{{item}}/{{page}} &nbsp;</span></div>
                            <div class="stl_01" style="top:7.4837em; left:64.2em;"><span class="stl_12 stl_11 stl_09">征免 &nbsp;</span></div>
                            <div class="stl_01" style="top:7.4837em; left:3.4592em;"><span class="stl_12 stl_11 stl_09">项号 &nbsp;</span></div>
                            <div class="stl_01" style="top:7.4837em; left:5.7125em;"><span class="stl_12 stl_11 stl_09">商品编号 &nbsp;</span></div>
                            <div class="stl_01" style="top:7.4837em; left:14.5408em;"><span class="stl_12 stl_11 stl_09">商品名称及规格型号 &nbsp;</span></div>
                            <div class="stl_01" style="top:7.4837em; left:35.7842em;"><span class="stl_12 stl_11 stl_09">单价/总价/币制 &nbsp;</span></div>
                            <div class="stl_01" style="top:7.4837em; left:42.4908em;"><span class="stl_12 stl_11 stl_09">原产国(地区) &nbsp;</span></div>
                            <div class="stl_01" style="top:7.4837em; left:47.9408em;"><span class="stl_12 stl_11 stl_09">最终目的国(地区) &nbsp;</span></div>
                            <div class="stl_01" style="top:7.4837em; left:56.8683em;"><span class="stl_12 stl_11 stl_09">境内货源地 &nbsp;</span></div>
                            <div style="position: absolute;top:8.6138em;left:3.125em;">
                                <ul class="product-list" v-for="(product,index) in ruleForm.productDetail" v-if="index>(5+14*(item-2))&&index<(6+14*(item-1))">
                                    <li class="item1">
                                        <span class="stl_13 stl_11 stl_09">{{index+1}}</span>
                                        <span class="stl_13 stl_11 stl_09">(0)</span>
                                    </li>
                                    <li class="item2">
                                        <span class="stl_13 stl_11 stl_09">{{product.hsCode}}</span>
                                    </li>
                                    <li class="item3">
                                        <span class="stl_13 stl_11 stl_09">{{product.productName}}</span>
                                        <span class="stl_13 stl_11 stl_09">{{product.property|filterProperty}}</span>
                                    </li>
                                    <li class="item4">
                                        <span class="stl_13 stl_11 stl_09">{{product.volumeTrade}}{{product.cjUnitId|filterCjUnit|filterLabel}}</span>
                                        <span class="stl_13 stl_11 stl_09"></span>
                                        <span class="stl_13 stl_11 stl_09">{{product.volumeTrade}}{{product.cjUnitId|filterCjUnit|filterLabel}}</span>
                                    </li>
                                    <li class="item5">
                                        <span class="stl_13 stl_11 stl_09">{{product.unitPrice}}</span>
                                        <span class="stl_13 stl_11 stl_09">{{product.totalPrices}}</span>
                                        <span class="stl_13 stl_11 stl_09">{{product.currencyId|moneyUnit}}</span>
                                    </li>
                                    <li class="item6">
                                        <span class="stl_13 stl_11 stl_09">中国</span>
                                        <span class="stl_13 stl_11 stl_09">(CHN)</span>
                                    </li>
                                    <li class="item7">
                                        <span class="stl_13 stl_11 stl_09">{{ruleForm.destinCountryName}}</span>
                                        <span class="stl_13 stl_11 stl_09">({{ruleForm.destinCountryId}})</span>
                                    </li>
                                    <li class="item8">
                                        <span class="stl_13 stl_11 stl_09">({{product.churProAddId}}})义乌照章征税</span>
                                        <span class="stl_13 stl_11 stl_09">(1)</span>
                                    </li>
                                </ul>
                            </div>
                        </template>

                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script>
    export default {
        name: "",
        data: function () {
            return {
                ruleForm: {
                    orderId:'',
                    productDetail:[]
                },
            }
        },
        computed:{
            page:function () {
                let page = 1;
                if(this.ruleForm.productDetail&&this.ruleForm.productDetail.length>6){
                    page+=Math.ceil((this.ruleForm.productDetail.length-6)/14)
                }
                return page;
            },
            len:function () {
                return this.ruleForm.productDetail?this.ruleForm.productDetail.length:0;
            }
        },
        mounted(){
            if(this.$route.query.id){
                this.ruleForm.orderId = this.$route.query.id;
                this.getDetail();
            }
        },
        methods: {

            getDetail() {
                let self = this, url = '/trade/comreceiptorder/info/' + self.ruleForm.orderId;
                self.$axios.get(url).then((res) => {
                    if (res.code == 0) {
                        self.ruleForm = self.$util.extend(res.comReceiptOrder);
                    }
                });
            },

            //确认
            confirm(status){
                let self = this;
                //1确认无误2有误退回
                if(status==1){
                    self.$confirm('是否确认无误', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                    }).then(() => {
                        self.$axios.get('/trade/comreceiptorder/confirm/'+self.ruleForm.orderId).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: "操作成功",
                                    type: 'success'
                                });
                                self.back();
                            }
                        });
                    }).catch(() => {

                    });
                }else{
                    self.$prompt('请输入退回备注', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        inputPattern:  /\S/,
                        inputErrorMessage: '备注不能为空'
                    }).then(({ value }) => {
                        self.$axios.post('/trade/comreceiptorder/unconfirm',{orId:self.ruleForm.orderId,remark:value}).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: "操作成功",
                                    type: 'success'
                                });
                                self.back();
                            }
                        });
                    }).catch(() => {

                    });
                }
            },
            //单据退回
            rollback(){
                let self = this;
                self.$prompt('请输入退回备注', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputType:'textarea',
                    inputPattern: /\S/,
                    inputErrorMessage: '备注不能为空'
                }).then(({ value }) => {
                    self.$axios.post('/trade/comreceiptorder/rollback',{
                        orderId:self.ruleForm.orderId,
                        remark:value
                    }).then((res) => {
                        if(res.code==0){
                            self.$message({
                                message: "操作成功",
                                type: 'success'
                            });
                            self.back();
                        }
                    });
                }).catch(() => {

                });
            },
            //冻结解冻订单
            frozen(){
                let self = this;
                let str = self.ruleForm.frozen==1?'是否确认解冻订单':'是否确认冻结订单';//是否冻结0否1是
                let frozen = self.ruleForm.frozen==1?'0':'1';
                this.$confirm(str, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                }).then(() => {
                    self.$axios.get('/trade/comreceiptorder/frozenConfirm',{
                        params:{
                            orderId:self.ruleForm.orderId,
                            frozen:frozen
                        }
                    }).then((res) => {
                        if(res.code==0){
                            self.$message({
                                message: "操作成功",
                                type: 'success'
                            });
                            self.back();
                        }
                    });
                }).catch(() => {

                });
            },
            back(){
                this.$store.commit('CollapseSidebar',false);//打开侧边栏
                this.$router.replace({path: '/home/<USER>/list'});
            }
        }
    }
</script>

<style scoped>
    .confirm-status{background: rgba(255, 39, 65, 1);height: 30px;line-height: 30px;padding: 0 10px;display: inline-block;color: #FFFF00;font-weight: bold}
    .confirm-status.cur{background:rgba(0, 128, 0, 1)}
    .product-list{
        overflow:hidden;margin-top: 0em;
    }
    .product-list li{float: left;position: relative;}
    .product-list li span{display: block;margin-bottom: 0.075em;height: 1.12em}
    .product-list .item1{width: 1.9192em;}
    .product-list .item2{width: 4.2em;}
    .product-list .item3{width: 17em;}
    .product-list .item4{width: 8.4em;text-align: right}
    .product-list .item5{width: 7em;text-align: right}
    .product-list .item6{width: 5.76em;text-align: right}
    .product-list .item7{width: 6.4em;text-align: right}
    .product-list .item8{width: 12.7em;text-align: right}
    sup {
        position: relative;
        top: -0.4em;
        vertical-align: baseline;
    }
    sub {
        position: relative;
        top: 0.4em;
        vertical-align: baseline;
    }
    a:link {text-decoration:none;}
    a:visited {text-decoration:none;}
    @media screen and (min-device-pixel-ratio:0), (-webkit-min-device-pixel-ratio:0), (min--moz-device-pixel-ratio: 0) { .view { font-size:10em; transform:scale(0.1); -moz-transform:scale(0.1); -webkit-transform:scale(0.1); -moz-transform-origin:top left; -webkit-transform-origin:top left; } }
    .layer { }.ie { font-size: 1pt; }
    .ie body { font-size: 12em; }
    .stl_01 {
        position: absolute;
        white-space: nowrap;
    }
    .stl_02 {
        height: 49.58333em;
        font-size: 1em;
        margin: 0em;
        line-height: 0.0em;
        display: block;
        border-style: none;
        width: 70.16666em;
    }
    .stl_03 {
        position: relative;
    }
    .stl_04 {
        position: absolute;
        left: 0em;
        top: 0em;
    }
    .stl_05 {
        position: relative;
        width: 70.16666em;
    }
    .stl_06 {
        height: 4.958333em;
    }
    .ie .stl_06 {
        height: 49.58333em;
    }
    @font-face {
        font-family:"DAPHUN+font000000002286acb6";
        src:url("../../../../static/pdf/hwbgd/font2.woff") format("woff");
    }
    .stl_07 {
        font-size: 0.5em;
        /*font-family: "DAPHUN+font000000002286acb6", "Times New Roman";*/
        color: #000000;
    }
    .stl_08 {
        line-height: 4.766em;
    }
    .stl_09 {
        letter-spacing: 0em;
    }

    .ie .stl_09 {
        letter-spacing: 0px;
    }
    @font-face {
        font-family:"KKRVCM+font000000002286acb6";
        src:url("../../../../static/pdf/hwbgd/font1.woff") format("woff");
    }
    .stl_10 {
        font-size: 1.5em;
        /*font-family: "KKRVCM+font000000002286acb6";*/
        color: #000000;
    }
    .stl_11 {
        line-height: 1em;
    }
    .stl_12 {
        font-size: 0.625em;
        /*font-family: "KKRVCM+font000000002286acb6";*/
        color: #323232;
    }
    .stl_13 {
        font-size: 0.75em;
        /*font-family: "KKRVCM+font000000002286acb6";*/
        color: #000000;
    }
    .stl_14 {
        font-size: 0.75em;
        /*font-family: "KKRVCM+font000000002286acb6";*/
        color: #323232;
    }
    .stl_15 {
        font-size: 0.625em;
        /*font-family: "KKRVCM+font000000002286acb6";*/
        color: #333333;
    }
    .stl_16 {
        letter-spacing: 0.009em;
    }

    .ie .stl_16 {
        letter-spacing: 0.1077px;
    }
    .stl_17 {
        letter-spacing: 0.0219em;
    }

    .ie .stl_17 {
        letter-spacing: 0.2622px;
    }
    .stl_18 {
        letter-spacing: 0.0083em;
    }

    .ie .stl_18 {
        letter-spacing: 0.1px;
    }
    .stl_19 {
        letter-spacing: 0.0097em;
    }

    .ie .stl_19 {
        letter-spacing: 0.1167px;
    }
</style>
