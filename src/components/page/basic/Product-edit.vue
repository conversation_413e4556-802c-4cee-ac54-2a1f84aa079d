<template>
    <div class="white-bg">
        <div class="modal-header">
            <p class="h-title"><span v-if="ruleForm.pid">编辑</span><span v-else>新建</span>商品</p>
        </div>
        <div class="modal-content">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="HSCODE:" prop="hsCode">
                            <el-input v-model="ruleForm.hsCode"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="商品名称:" prop="productName">
                            <el-input v-model="ruleForm.productName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="英文名称:" prop="productEnName">
                            <el-input v-model="ruleForm.productEnName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="原产国:" prop="countryId">
                            <country v-model="ruleForm.countryId" @change="country=>{[ruleForm.countryId,ruleForm.countryName]=country}" :countryId="ruleForm.countryId"></country>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="单价$:" prop="productPrice">
                            <el-input v-model="ruleForm.productPrice"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="重量KG:" prop="productWeight">
                            <el-input v-model="ruleForm.productWeight"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="法定单位:" prop="faUnit">
                            <unit v-model="ruleForm.faUnit" @change="unit=>{[ruleForm.faUnit,ruleForm.faUnitName]=unit}" :id="ruleForm.faUnit" :name="ruleForm.faUnitName"></unit>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="状态:" prop="productStuats">
                            <el-radio v-model="ruleForm.productStuats" label="1">启用</el-radio>
                            <el-radio v-model="ruleForm.productStuats" label="0">停用</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">确认</el-button>
                    <el-button @click="back()">取消</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>
<script>
    export default {
        data: function () {
            return {
                ruleForm: {
                    id:'',
                    hsCode:'',
                    productName:'',
                    productEnName:'',
                    countryId:'',
                    countryName:'',
                    faUnit:'',
                    faUnitName:'',
                    productPrice:'',
                    productWeight:'',
                    exId:'',
                    exName:'',
                    productStuats:'1',
                },
                rules: {
                    hsCode: [
                        { required: true, message: '请填写HSCODE', trigger: 'blur' },
                        { pattern:/^[+]{0,1}(\d+)$/, message: '请填写数字', trigger: 'blur'}
                    ],
                    productName: [
                        { required: true, message: '请填写商品名称', trigger: 'blur' }
                    ],
                    countryId: [
                        { required: true, message: '请选择原产国', trigger: 'change' }
                    ],
                    faUnit: [
                        { required: true, message: '请选择法定单位', trigger: 'change' }
                    ],
                    productPrice:[
                        { required: true, message: '请输入单价', trigger: 'blur' },
                        { pattern:/^\d+(\.\d+)?$/, message: '请填写正确的数字', trigger: 'blur'},
                    ],
                    productWeight:[
                        { required: true, message: '请输入重量', trigger: 'blur' },
                        { pattern:/^\d+(\.\d+)?$/, message: '请填写正确的数字', trigger: 'blur'},
                    ],
                },
            }
        },
        mounted(){
            if(this.$route.query.id){
                this.ruleForm.id = this.$route.query.id;
                this.getDetail();
            }
        },
        methods:{
            getDetail(){
                let self = this,url = '/courier/comexproduct/info/'+self.ruleForm.id;
                self.$axios.get(url).then((res) => {
                    if(res.code==0){
                        for(let attr in self.ruleForm){
                            self.$set(self.ruleForm,attr,res.comExProduct[attr])
                        }
                    }
                });
            },
            submitForm(formName) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        let url;
                        if(self.ruleForm.id){
                            url ='/courier/comexproduct/update';
                        }else{
                            url ='/courier/comexproduct/save';
                        }
                        self.$axios.post(url,self.ruleForm).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: '操作成功',
                                    type: 'success'
                                });
                                self.$router.go(-1);
                            }
                        })
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            back(){
                this.$router.go(-1);
            }
        },
    }
</script>
