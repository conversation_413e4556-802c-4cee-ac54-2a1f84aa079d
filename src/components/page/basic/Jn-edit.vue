<template>
  <div class="white-bg">
    <div class="modal-header" v-if="isComponents">
      <p class="h-title">
        <span v-if="ruleForm.did">编辑</span
        ><span v-else>新建</span>境内收发货人
      </p>
    </div>
    <div class="modal-content">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="140px"
        class="demo-ruleForm"
      >
        <el-row :gutter="10">
          <el-col :span="18">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="OPENID:">
                  {{ ruleForm.openid }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发货票数:">
                  {{ ruleForm.sendNum }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="微信昵称:">
                  {{ ruleForm.wechatName }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发货总金额:">
                  {{ ruleForm.sendPrice }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="唯一身份码:">
                  {{ ruleForm.onlyCode }}
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>

          <el-col :span="6">
            <img class="avatar" :src="ruleForm.headImg" alt="" />
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="姓名:" prop="nickName">
              <el-input v-model="ruleForm.nickName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮编:" prop="postcode">
              <el-input v-model="ruleForm.postcode"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="手机号前缀:" prop="topNumId">
              <top-num
                v-model="ruleForm.topNumId"
                :id="ruleForm.topNumId"
                :num="ruleForm.topNum"
                @change="
                  (args) => {
                    ruleForm.topNumId = args[0];
                    ruleForm.topNum = args[1];
                  }
                "
              ></top-num>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号:" prop="mobile">
              <el-input v-model="ruleForm.mobile"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="公司名称:" prop="companyName">
              <el-input v-model="ruleForm.companyName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="十位编码/身份证:" prop="tenCode">
              <el-input v-model="ruleForm.tenCode"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="所属业务代理:" prop="businessUserIds">
              <agent
                v-model="ruleForm.businessUserIds"
                @change="(val) => (ruleForm.businessUserName = val[1])"
                multiple
                :id="ruleForm.businessUserIds"
                placeholder="请选择所属业务代理"
              ></agent>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="默认发货人:" prop="defaultFlag">
              <el-radio v-model="ruleForm.defaultFlag" :label="1">是</el-radio>
              <el-radio v-model="ruleForm.defaultFlag" :label="0">否</el-radio>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="收发货地址省:" prop="provinceId">
              <el-select
                v-model="ruleForm.provinceId"
                @change="changeProvince"
                filterable
                placeholder="请选择省"
              >
                <el-option
                  v-for="item in province"
                  :label="item.name"
                  :value="item.adcode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="市:" prop="cityId">
              <el-select
                v-model="ruleForm.cityId"
                @change="changeCity"
                filterable
                placeholder="请选择市"
              >
                <el-option
                  v-for="item in city"
                  :label="item.name"
                  :value="item.adcode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="区:" prop="areaId">
              <el-select
                v-model="ruleForm.areaId"
                @change="changeArea"
                filterable
                placeholder="请选择区"
              >
                <el-option
                  v-for="item in area"
                  :label="item.name"
                  :value="item.adcode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="详细地址:" prop="address">
              <el-input
                v-model="ruleForm.address"
                style="max-width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>

        <el-table :data="ruleForm.remarkList" style="width: 100%">
          <el-table-column type="index" label="序号" width="60px" />
          <el-table-column prop="businessAccount" label="业务代理编号" />
          <el-table-column prop="businessUserName" label="业务代理姓名" />
          <el-table-column prop="putName" label="业务代理备注名字">
            <template slot-scope="scope">
              <el-tag
                closable
                v-for="(item, index) in scope.row.listName"
                :key="item.id"
                @close="handleUpdatePutName(scope.row, index, scope.$index)"
              >
                {{ item.putName }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <el-form-item class="mt_2">
          <el-button type="primary" @click="submitForm('ruleForm')"
            >确认</el-button
          >
          <el-button @click="back()">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import topNum from "components/components/topNum";
export default {
  props: {
    isComponents: {
      type: Boolean,
      default: false,
    },
    callback: {
      type: Function,
      default: function () {},
    },
  },
  components: {
    topNum,
  },
  data: function () {
    return {
      topNum: [],
      province: [],
      city: [],
      area: [],
      ruleForm: {
        did: "",
        nickName: "",
        onlyCode: "",
        headImg: "",
        // idCard: '',
        companyName: "",
        tenCode: "",
        mobile: "",
        email: "",
        address: "",
        openid: "",
        wechatName: "",
        sendNum: "",
        sendPrice: "",
        country: "中国",
        countryId: "100000",
        province: "",
        provinceId: "",
        city: "",
        cityId: "",
        area: "",
        areaId: "",
        postcode: "",
        exId: "",
        belongUid: "",
        topNumId: "",
        topNum: "",
        businessUserIds: "",
        businessUserName: "",
        defaultFlag: 0,
        remarkList: [],
      },
      rules: {
        // nickName: [
        //     { required: true, message: '请填写姓名', trigger: 'blur' }
        // ],
        // topNumId: [
        //     { required: true, message: '请选择区号', trigger: 'change' }
        // ],
        // mobile: [
        //     { required: true, message: '请填写联系电话', trigger: 'blur' }
        // ],
        // businessUserId: [
        //     { required: true, message: '请选择所属业务代理', trigger: 'change' }
        // ],
        // provinceId: [
        //     { required: true, message: '请选择省', trigger: 'change' }
        // ],
        // cityId: [
        //     { required: true, message: '请选择市', trigger: 'change' }
        // ],
        // areaId: [
        //     { required: true, message: '请选择区', trigger: 'change' }
        // ],
        // address: [
        //     { required: true, message: '请填写收发货地址', trigger: 'blur' }
        // ],
      },
    };
  },
  mounted() {
    if (this.$route.query.id && !this.isComponents) {
      this.ruleForm.did = this.$route.query.id;
      this.getDetail();
    } else {
      this.ruleForm.topNum = "86";
      this.ruleForm.topNumId = 26;
    }
    this.getTopNum();
    this.getProvince();
  },
  methods: {
    getDetail() {
      let self = this,
        url = "/courier/jbrjndeliveruser/info/" + self.ruleForm.did;
      self.$axios.get(url).then((res) => {
        if (res.code == 0) {
          for (let attr in self.ruleForm) {
            self.$set(self.ruleForm, attr, res.jbrJnDeliverUser[attr]);
          }
          this.getCity();
          this.getArea();
        }
      });
    },
    handleUpdatePutName(row, index, pIndex) {
      this.$axios({
        url: "/sys/jbrjnbusinessname/delete",
        method: "post",
        data: [row.listName[index].id],
        headers: { closeLoading: true },
      }).then((res) => {
        if (res.code == 0) {
          this.$message({
            message: "操作成功",
            type: "success",
          });
          row.listName.splice(index, 1);
          if (!row.listName.length) {
            this.ruleForm.remarkList.splice(pIndex, 1);
          }
        }
      });
    },
    getProvince() {
      // areaType//1国内，2国外（不包含中国，3全球（包含中国
      this.$axios({
        url: "/sys/area/list",
        method: "post",
        data: { parenCode: 100000, areaType: "1" },
        headers: { closeLoading: true },
      }).then((res) => {
        if (res.code == 0) {
          this.province = res.areaList;
        }
      });
    },
    getCity() {
      this.$axios({
        url: "/sys/area/list",
        method: "post",
        data: { parenCode: this.ruleForm.provinceId, areaType: "1" },
        headers: { closeLoading: true },
      }).then((res) => {
        if (res.code == 0) {
          this.city = res.areaList;
        }
      });
    },
    getArea() {
      this.$axios({
        url: "/sys/area/list",
        method: "post",
        data: { parenCode: this.ruleForm.cityId, areaType: "1" },
        headers: { closeLoading: true },
      }).then((res) => {
        if (res.code == 0) {
          this.area = res.areaList;
        }
      });
    },
    changeProvince(vId) {
      let obj = {};
      obj = this.province.find((item) => {
        return item.adcode === vId;
      });
      if (obj) {
        this.ruleForm.province = obj.name;
      }
      this.ruleForm.city = "";
      this.ruleForm.cityId = "";
      this.ruleForm.area = "";
      this.ruleForm.areaId = "";
      this.getCity();
    },
    changeCity(vId) {
      let obj = {};
      obj = this.city.find((item) => {
        return item.adcode === vId;
      });
      if (obj) {
        this.ruleForm.city = obj.name;
      }
      this.ruleForm.area = "";
      this.ruleForm.areaId = "";
      this.getArea();
    },
    changeArea(vId) {
      let obj = {};
      obj = this.area.find((item) => {
        return item.adcode === vId;
      });
      if (obj) {
        this.ruleForm.area = obj.name;
      }
    },
    getTopNum() {
      this.$axios({
        url: "/sys/topnummobile/list",
        method: "get",
        headers: { closeLoading: true },
      }).then((res) => {
        if (res.code == 0) {
          this.topNum = res.list;
        }
      });
    },
    submitForm(formName) {
      let self = this;
      self.$refs[formName].validate((valid) => {
        if (valid) {
          let url = self.ruleForm.did
            ? "/courier/jbrjndeliveruser/update"
            : "/courier/jbrjndeliveruser/save";
          const ruleForm = JSON.parse(JSON.stringify(self.ruleForm));
          delete ruleForm.remarkList;
          self.$axios.post(url, ruleForm).then((res) => {
            if (res.code == 0) {
              self.$message({
                message: "操作成功",
                type: "success",
              });
              if (this.isComponents) {
                this.callback();
              } else {
                this.$router.go(-1);
              }
            }
          });
        } else {
          self.$message({
            message: "请正确填写",
            type: "error",
          });
          return false;
        }
      });
    },
    back() {
      if (this.isComponents) {
        this.callback();
      } else {
        this.$router.go(-1);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.avatar {
  width: 100px;
  height: 100px;
}
</style>
