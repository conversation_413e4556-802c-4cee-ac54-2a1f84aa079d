<template>
    <div>
        <v-search :searchFun="getList" :resetFun="resetSearch">
            <template slot="form1">
                <el-col :span="6">
                    <el-form-item label="收发货人姓名" label-width="120px">
                        <el-input v-model="params.domName" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="电话/手机">
                        <el-input v-model="params.mobile" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="微信昵称">
                        <el-input v-model="params.nickname" placeholder="请输入内容"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="所属业务代理" label-width="120px">
                        <agent v-model="params.businessUserId" :id="params.businessUserId" placeholder="请选择所属业务代理"></agent>
                    </el-form-item>
                </el-col>
            </template>
        </v-search>
        <div class="p_1">
            <el-row class="mb_1 text-right">
                <el-button type="primary" @click="handleEdit('')">新增</el-button>
            </el-row>
            <v-table
                :tableData="tableData"
                :params="params"
                :sortChange="sortChange"
                :handleSizeChange="handleSizeChange"
                :handleCurrentChange="handleCurrentChange"
                :rowKey="rowKey"
                :total="total"
                :tableHeader="tableHeader"
                :dropTableHeader="dropTableHeader"
            ></v-table>
        </div>

    </div>
</template>

<script>
    import vSearch from '../public/Search';
    import vTable from '../public/Table';


    export default {
        data() {
            return {
                params:{
                    page:1,
                    limit: 50,
                    sidx:'',
                    order:'',
                    domName:'',
                    mobile:'',
                    nickname:'',
                    businessUserId:''
                },
                rowKey:'did',
                total:0,
                totalPage:1,
                tableData: [],
                tableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'nickName', label: '姓名',sortable:'custom',minWidth:'140px'},
                    { prop: 'mobile', label: '电话/手机号',sortable:'custom',minWidth:'140px'},
                    { prop: 'postcode', label: '邮编',sortable:'custom',minWidth:'140px'},
                    { prop: 'address', label: '收发货地址',sortable:'custom',minWidth:'200px'},
                    { prop: 'defaultFlag', label: '默认发货人',sortable:'custom', formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }, minWidth:'140px'},
                    { prop: 'openid', label: 'OPENID',sortable:'custom',minWidth:'200px'},
                    { prop: 'wechatName', label: '微信昵称',sortable:'custom',minWidth:'140px'},
                    { prop: 'businessUserName', label: '所属业务代理',sortable:'custom',minWidth:'140px'},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ],
                dropTableHeader: [
                    { index: true, label: '序号'},
                    { prop: 'nickName', label: '姓名',sortable:'custom',minWidth:'140px'},
                    { prop: 'mobile', label: '电话/手机号',sortable:'custom',minWidth:'140px'},
                    { prop: 'postcode', label: '邮编',sortable:'custom',minWidth:'140px'},
                    { prop: 'address', label: '收发货地址',sortable:'custom',minWidth:'200px'},
                    { prop: 'defaultFlag', label: '默认发货人',sortable:'custom', formatData: function(val) { return val == 0 ? '否' :val == 1 ? '是' : '未知'; }, minWidth:'140px'},
                    { prop: 'openid', label: 'OPENID',sortable:'custom',minWidth:'200px'},
                    { prop: 'wechatName', label: '微信昵称',sortable:'custom',minWidth:'140px'},
                    { prop: 'businessUserName', label: '所属业务代理',sortable:'custom',minWidth:'140px'},
                    { prop: 'oper', label: '操作', fixed: 'right',
                        oper: [
                            { class: 'edit', clickFun: this.handleEdit },
                            { class: 'delete', clickFun: this.handleDelete }
                        ]
                    },
                ]
            }
        },
        mounted(){
            if(this.$store.state.listParams.has(this.$route.path)) {
                this.$set(this,'params',this.$store.state.listParams.get(this.$route.path))
            }
            this.getList();
        },
        methods: {
            getList() {
                let self = this;
                this.$store.dispatch('saveListParams',{path:this.$route.path,params:self.params});
                self.$axios.get('/courier/jbrjndeliveruser/list',{params: self.params}).then((res) => {
                    self.tableData = res.page.list;
                    self.total = parseInt(res.page.totalCount);
                    self.totalPage = parseInt(res.page.totalPage);
                    self.tableData.map(item=>{
                        item.address=`${item.province || ''}${item.city || ''}${item.area || ''}${item.address || ''}`;
                        item.mobile=item.topNum+' '+item.mobile;
                    })
                });
            },
            handleSizeChange(val) {
                this.params.limit = val;
                this.getList()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.params.page = val;
                this.getList()
                console.log(`当前页: ${val}`);
            },
            resetSearch(){
                for(let name in this.params) {
                    if(name=='page'){
                        this.$set(this.params,name,1);
                    }else if(name=='limit'){
                        this.$set(this.params,name,50);
                    }else{
                        this.$set(this.params,name,'');
                    }
                }
                this.getList();
            },
            sortChange(column, prop, order) {
                this.params.sidx = column.prop?column.prop:'';
                this.params.order = column.order?column.order.substring(0,(column.order.indexOf("c")+1)):'';
                this.getList()
            },
            handleEdit(row){
                this.$router.push({path: '/home/<USER>/jn/edit',query:{id:row.did}})
            },
            handleDelete(row){
                this.$confirm('是否确认删除该境内收发货人?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    center: true
                }).then(() => {
                    this.$axios.post('/courier/jbrjndeliveruser/delete',[row.did]).then((res) => {
                        if(res.code==0){
                            this.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            this.getList();
                        }
                    });
                }).catch(() => {

                });
            },
        },
        components:{
            vSearch,
            vTable
        }
    }
</script>

