<template>
  <div class="p_1">
    <el-row class="mb_1 text-right">
      <el-button type="primary" @click="handleEdit('')">新增</el-button>
    </el-row>
    <div class="mt_1 mb_1">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="params.page"
          :page-sizes="[50, 100, 300, 500]"
          :page-size="params.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
    </div>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column align="center" fixed="left" type="index" label="序号" width="60"></el-table-column>
      <el-table-column align="center" prop="supplierNum" label="供应商编号" min-width="120"></el-table-column>
      <el-table-column align="center" prop="supplierName" label="供应商名称" min-width="120"></el-table-column>
      <el-table-column align="center" prop="supplierType" label="供应商类型" min-width="120">
        <template slot-scope="scope">
          {{scope.row.supplierType | supplierType}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="contactPerson" label="联系人" min-width="120"></el-table-column>
      <el-table-column align="center" prop="exName" label="联系电话" min-width="120">
        <template slot-scope="scope">{{ (scope.row.topNum ? '+' + scope.row.topNum + ' ' : '') + scope.row.mobile}}</template>
      </el-table-column>
      <el-table-column align="center" prop="relationship" label="状态" min-width="80">
        <template slot-scope="scope">
          {{scope.row.relationship === 1 ? '正常' : '已结束'}}
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" prop="exName" label="操作" min-width="140">
        <template slot-scope="scope">
          <el-button type="primary" @click="handleEdit(scope.row)" size="mini" icon="el-icon-edit">编辑</el-button>
          <el-button type="danger" @click="handleDelete(scope.row)" size="mini" icon="el-icon-delete">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="供应商管理" :visible.sync="dialogVisible" center>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="供应商编号" prop="supplierNum">
              <el-input v-model="ruleForm.supplierNum"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="ruleForm.contactPerson"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="供应商名称" prop="supplierName">
              <el-input v-model="ruleForm.supplierName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商类型" prop="supplierType">
              <el-select v-model="ruleForm.supplierType" placeholder="请选择供应商类型" @change="handleTypeChange">
                <el-option
                  v-for="item in STATUS.supplierType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="手机号前缀" prop="topNumId">
              <top-num v-model="ruleForm.topNumId" :id="ruleForm.topNumId" :num="ruleForm.topNum" @change="args => { ruleForm.topNumId = args[0];ruleForm.topNum = args[1];}"></top-num>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="mobile">
              <el-input v-model="ruleForm.mobile"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="合作关系" prop="relationship">
              <el-radio v-model="ruleForm.relationship" :label="1">正常</el-radio>
              <el-radio v-model="ruleForm.relationship" :label="2">已结束</el-radio>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="ruleForm.supplierType === 2">
            <el-form-item label="除数" prop="divisor">
              <el-input v-model="ruleForm.divisor"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="备注" prop="chName">
              <el-input type="textarea" :rows="3" placeholder="请输入备注" v-model="ruleForm.remark"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog">取 消</el-button>
        <el-button type="primary" @click="enterDialog('ruleForm')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import vSearch from "../public/Search";
import topNum from 'components/components/topNum'
import { GetList, Delete, Save, Update } from "src/api/supplier";
export default {
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        id: "",
        supplierNum: "",
        supplierName: "",
        supplierType: "",
        contactPerson: "",
        topNumId: "",
        topNum: "",
        mobile: "",
        relationship: 1,
        divisor: "",
        remark: "",
      },
      rules: {
        supplierNum: [
          { required: true, message: "请填写供应商编号", trigger: "blur" },
          { pattern: /^[a-zA-Z0-9]{1,20}$/, message: "请输入正确的编号(英文数字)", trigger: "blur" }
        ],
        supplierName: [
          { required: true, message: "请填写供应商名称", trigger: "blur" },
        ],
        supplierType: [
          { required: true, message: "请选择供应商类型", trigger: "change" },
        ],
        divisor: [
          { required: true, message: "请输入除数", trigger: "blur" },
          { pattern: /^\d+(\.\d+)?$/, message: '请输入正确的数字', trigger: 'blur'}
        ],
        mobile: [
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" },
        ],
      },
      params: {
        page: 1,
        limit: 50,
        sidx: "",
        order: "",
      },
      rowKey: "chId",
      total: 0,
      totalPage: 1,
			tableData: [],
			topNum: [],
    };
  },
  mounted() {
		this.getList();
		this.getTopNum()
  },
  methods: {
    getList() {
      GetList(this.params).then((res) => {
        this.tableData = res.page.list;
        this.total = parseInt(res.page.totalCount);
        this.totalPage = parseInt(res.page.totalPage);
      });
    },
    getTopNum() {
      this.$axios({
        url: "/sys/topnummobile/list",
        method: "get",
        headers: { closeLoading: true },
      }).then((res) => {
        if (res.code == 0) {
          this.topNum = res.list;
        }
      });
    },
    handleSizeChange(val) {
      this.params.limit = val;
      this.getList();
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.params.page = val;
      this.getList();
      console.log(`当前页: ${val}`);
    },
    resetSearch() {
      for (let name in this.params) {
        if (name == "page") {
          this.$set(this.params, name, 1);
        } else if (name == "limit") {
          this.$set(this.params, name, 50);
        } else {
          this.$set(this.params, name, "");
        }
      }
      this.getList();
    },
    handleEdit(row) {
      this.dialogVisible = true;
      this.$nextTick((callback) => {
        if (row) {
            this.$set(this, 'ruleForm', row)
        } else {
          this.ruleForm = {
            id: "",
            supplierNum: "",
            supplierName: "",
            supplierType: "",
            contactPerson: "",
            topNumId: "",
            topNum: "",
            mobile: "",
            relationship: 1,
            divisor: "",
            remark: "",
          };
        }
      })
    },
    cancelDialog() {
      this.dialogVisible = false;
    },
    enterDialog(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const func = this.ruleForm.id ? Update : Save
          func(this.ruleForm).then((res) => {
            if (res.code == 0) {
              this.$message({
                message: "操作成功",
                type: "success",
              });
              this.getList();
              this.cancelDialog();
            }
          });
        } else {
          this.$message({
            message: "请正确填写",
            type: "error",
          });
          return false;
        }
      });
    },
    handleTypeChange(val) {
      if(val !== 2) this.ruleForm.divisor = ''
    },
    handleDelete(row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
        center: true,
      })
        .then(() => {
          Delete([row.id]).then((res) => {
            if (res.code == 0) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
            }
          });
        })
        .catch(() => {});
    },
  },
  components: {
    vSearch,
    topNum
  },
};
</script>

