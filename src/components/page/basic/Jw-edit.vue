<template>
    <div class="white-bg">
        <div class="modal-header" v-if="isComponents">
            <p class="h-title"><span v-if="ruleForm.did">编辑</span><span v-else>新建</span>境外收发货人</p>
        </div>
        <div class="modal-content">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="OPENID:">
                            {{ruleForm.openid}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="发货票数:">
                            {{ruleForm.sendNum}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="微信昵称:">
                            {{ruleForm.wechatName}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="发货总金额:">
                            {{ruleForm.sendPrice}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-divider></el-divider>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="境外收发货人:" prop="nickName">
                            <el-input v-model="ruleForm.nickName" @input="ruleForm.nickName = ruleForm.nickName.toUpperCase()"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="公司名称:" prop="companyName">
                            <el-input v-model="ruleForm.companyName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="手机号前缀:" prop="topNumId">
                            <top-num v-model="ruleForm.topNumId" :id="ruleForm.topNumId" :num="ruleForm.topNum" @change="args => { ruleForm.topNumId = args[0];ruleForm.topNum = args[1];}"></top-num>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话:" prop="mobile">
                            <el-input v-model="ruleForm.mobile"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="所属业务代理:" prop="businessUserId">
                            <agent v-model="ruleForm.businessUserId" @change="user=>{[ruleForm.businessUserId,ruleForm.businessUserName]=user}" :id="ruleForm.businessUserId" placeholder="请选择所属业务代理"></agent>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="十位编码/身份证:" prop="tenCode">
                            <el-input v-model="ruleForm.tenCode"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="国家:" prop="country">
                            <country v-model="ruleForm.country" @change="country=>{[ruleForm.countryId,ruleForm.country]=country;ruleForm.provinceId='';ruleForm.province='';ruleForm.cityId='';ruleForm.city='';getProvince()}" :countryName="ruleForm.country" :areaType="2" :countryId="ruleForm.countryId"></country>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="邮编:" prop="postcode">
                            <el-input v-model="ruleForm.postcode"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="8">
                        <el-form-item label="收发货地址省:" prop="provinceId">
                            <div class="flex">
                                <el-input v-if="provinceAdd" v-model="ruleForm.province" placeholder="请输入省"></el-input>
                                <el-select v-else v-model="ruleForm.provinceId" @change="changeProvince" default-first-option filterable placeholder="请选择省">
                                    <el-option
                                        v-for="item in province"
                                        :label="item.name"
                                        :value="item.adcode">
                                    </el-option>
                                </el-select>
                                <el-checkbox class="ml_1" v-model="provinceAdd" @change="handleAddAddress('province')">新增</el-checkbox>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="市:" prop="cityId">
                            <div class="flex">
                                <el-input v-if="cityAdd" v-model="ruleForm.city" placeholder="请输入市"></el-input>
                                <el-select v-else v-model="ruleForm.cityId" @change="changeCity" default-first-option filterable placeholder="请选择市">
                                    <el-option
                                        filterable
                                        allow-create
                                        default-first-option
                                        v-for="item in city"
                                        :label="item.name"
                                        :value="item.adcode">
                                    </el-option>
                                </el-select>
                                <el-checkbox class="ml_1" v-model="cityAdd" @change="handleAddAddress('city')">新增</el-checkbox>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="详细地址:" prop="address">
                            <el-input v-model="ruleForm.address" style="max-width: 100%"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="24">
                        <el-form-item label="备注:" prop="remark">
                            <el-input v-model="ruleForm.remark" style="max-width: 100%"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">确认</el-button>
                    <el-button @click="back()">取消</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>
<script>
    import topNum from 'components/components/topNum'
    export default {
        props:{
            isComponents:{
                type:Boolean,
                default:false
            },
            callback:{
                type:Function,
                default: function () {

                }
            }
        },
        components: {
            topNum
        },
        data: function () {
            return {
                provinceAdd: false,    //新增省
                cityAdd: false,    //新增市
                topNum:[],//手机号前缀
                province:[],
                city:[],
                ruleForm: {
                    did:'',
                    nickName: '',
                    mobile: '',
                    email: '',
                    address: '',
                    openid: '',
                    wechatName: '',
                    sendNum: '',
                    sendPrice: '',
                    country: '',
                    countryId: '',
                    province: '',
                    provinceId: '',
                    city: '',
                    cityId: '',
                    area: '',
                    areaId: '',
                    postcode: '',
                    tenCode: '',
                    exId: '',
                    belongUid: '',
                    topNumId: '',
                    topNum: '',
                    companyName: '',
                    remark: '',
                    businessUserId:'',
                    businessUserName:'',
                },
                rules: {
                    nickName: [
                        { required: true, message: '请填写境外收发货人', trigger: 'blur' }
                    ],
                    country: [
                        { required: true, message: '请选择国家', trigger: 'change' }
                    ],
                    // provinceId: [
                    //     { required: true, message: '请选择省', trigger: 'change' }
                    // ],
                    // cityId: [
                    //     { required: true, message: '请选择市', trigger: 'change' }
                    // ],
                    businessUserId: [
                        { required: true, message: '请选择所属业务代理', trigger: 'change' }
                    ],
                    topNumId: [
                        { required: true, message: '请选择区号', trigger: 'change' }
                    ],
                    mobile: [
                        { required: true, message: '请填写联系电话', trigger: 'blur' }
                    ],
                    // address: [
                    //     { required: true, message: '请填写收发货地址', trigger: 'blur' }
                    // ],
                },
            }
        },
        mounted(){
            if(this.$route.query.id&&!this.isComponents){
                this.ruleForm.did = this.$route.query.id;
                this.getDetail();
            }
            this.getTopNum();
            this.getProvince();
        },
        methods:{
            getDetail(){
                let self = this,url = '/courier/jbrjwdeliveruser/info/'+self.ruleForm.did;
                self.$axios.get(url).then((res) => {
                    if(res.code==0){
                        for(let attr in self.ruleForm){
                            self.$set(self.ruleForm,attr,res.jbrJwDeliverUser[attr])
                        }
                        this.getProvince()
                        this.getCity()
                    }
                });
            },
            getProvince(){
                if(this.ruleForm.countryId=='')return
                this.$axios({url:'/sys/area/list',method:'post',data:{parenCode: this.ruleForm.countryId,areaType:'2'}, headers:{closeLoading:true}}).then(res=>{
                    if(res.code==0){
                        this.province = res.areaList;
                    }
                })
            },
            getCity(){
                this.$axios({url:'/sys/area/list',method:'post',data:{parenCode: this.ruleForm.provinceId,areaType:'2'}, headers:{closeLoading:true}}).then(res=>{
                    if(res.code==0){
                        this.city = res.areaList;
                    }
                })
            },
            handleAddAddress(type) {
                if(type === 'province'){
                    this.ruleForm.province='';
                    this.ruleForm.provinceId='';
                }
                this.ruleForm.city='';
                this.ruleForm.cityId='';
            },
            changeProvince(vId){
                let obj = {};
                obj = this.province.find((item)=>{
                    return item.adcode === vId;
                });
                this.ruleForm.city='';
                this.ruleForm.cityId='';
                if(obj){
                    this.ruleForm.province = obj.name;
                    this.getCity();
                }else{//如果是新建的条目，将值赋给province
                    this.ruleForm.province = vId;
                }

            },
            changeCity(vId){
                let obj = {};
                obj = this.city.find((item)=>{
                    return item.adcode === vId;
                });
                if(obj){
                    this.ruleForm.city = obj.name;
                }else{
                    this.ruleForm.city = vId;
                }
            },
            getTopNum(){
                this.$axios({url:'/sys/topnummobile/list',method:'get', headers:{closeLoading:true}}).then(res=>{
                    if(res.code==0){
                        this.topNum = res.list;
                    }
                })
            },
            submitForm(formName) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        let url = self.ruleForm.did?'/courier/jbrjwdeliveruser/update':'/courier/jbrjwdeliveruser/save';
                        let params = self.$util.extend(self.ruleForm);
                        params.provinceId = params.province && !params.provinceId ? 0 : params.provinceId;
                        params.cityId = params.city && !params.cityId ? 0 : params.cityId;
                        self.$axios.post(url,params).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: '操作成功',
                                    type: 'success'
                                });
                                if(this.isComponents){
                                    this.callback();
                                }else{
                                    this.$router.go(-1);
                                }
                            }
                        })
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            back(){
                if(this.isComponents){
                    this.callback();
                }else{
                    this.$router.go(-1);
                }
            }
        },
    }
</script>
