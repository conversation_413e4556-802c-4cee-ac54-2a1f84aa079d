<template>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" class="demo-ruleForm">
        <el-row :gutter="10">
            <el-col :span="12">
                <el-form-item label="联系人:" prop="username">
                    <el-input v-model="ruleForm.username"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="公司名称:" prop="companyName">
                    <el-input v-model="ruleForm.companyName"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="10">
            <el-col :span="12">
                <el-form-item label="联系电话:" prop="moblie">
                    <el-input v-model="ruleForm.moblie"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="十位编码/身份证:" prop="tenCode">
                    <el-input v-model="ruleForm.tenCode"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="10">
            <el-col :span="12">
                <el-form-item label="EMAIL:" prop="email">
                    <el-input v-model="ruleForm.email"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="10">
            <el-col :span="24">
                <el-form-item label="公司地址:" prop="address">
                    <el-input v-model="ruleForm.address" style="max-width: 100%"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-form-item>
            <el-button type="primary" @click="submitForm('ruleForm')">确认</el-button>
            <el-button @click="back()">取消</el-button>
        </el-form-item>
    </el-form>
</template>
<script>
    export default {
        data: function () {
            return {
                ruleForm: {
                    id: '',
                    username: '',
                    companyName: '',
                    moblie: '',
                    tenCode: '',
                    email: '',
                    address: '',
                    exId: '',
                    exName: ''
                },
                rules: {
                    username: [
                        { required: true, message: '请填写联系人', trigger: 'blur' }
                    ],
                    moblie: [
                        { required: true, message: '请填写联系电话', trigger: 'blur' },
                        { pattern:/^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
                    ],
                    address: [
                        { required: true, message: '请填写公司地址', trigger: 'blur' }
                    ],
                },
            }
        },
        mounted(){
            this.getDetail();
        },
        methods:{
            getDetail(){
                this.$axios.get('/courier/invoice/info').then((res) => {
                    if(res.code==0){
                        if(res.invoice){
                            for(let attr in this.ruleForm){
                                if(res.invoice[attr])this.$set(this.ruleForm,attr,res.invoice[attr])
                            }
                        }
                    }
                });
            },
            submitForm(formName) {
                let self = this;
                self.$refs[formName].validate((valid) => {
                    if (valid) {
                        self.$axios.post('/courier/invoice/update',self.ruleForm).then((res) => {
                            if(res.code==0){
                                self.$message({
                                    message: '操作成功',
                                    type: 'success'
                                });
                            }
                        })
                    } else {
                        self.$message({
                            message: '请正确填写',
                            type: 'error'
                        });
                        return false;
                    }
                });
            },
            back(){
                this.$router.go(-1);
            }
        },
    }
</script>
