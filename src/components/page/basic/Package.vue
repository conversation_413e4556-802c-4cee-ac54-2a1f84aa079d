<template>
  <div class="p_1">
    <div>
      <el-row class="mb_1 text-right">
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </el-row>
    </div>
    <div class="table-container">
      <div class="mt_1 mb_1">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="params.page"
          :page-sizes="[50, 100, 300, 500]"
          :page-size="params.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
      <el-table class="table-nopadding" :data="tableData" border style="width: 100%">
        <el-table-column align="center" fixed="left" type="index" label="序号" width="60"></el-table-column>
        <el-table-column align="center" prop="pkgType" label="包装物" min-width="100">
          <template slot-scope="scope">
            <template v-if="scope.row.isEdit">
							<el-select v-model="scope.row.pkgType" placeholder="请选择包装物类型">
                <el-option
                  v-for="item in STATUS.packageType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </template>
            <template v-else>{{scope.row.pkgType | packageType}}</template>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="pkgModel" label="规格型号" min-width="100">
          <template slot-scope="scope">
            <template v-if="scope.row.isEdit">
              <el-input v-model="scope.row.pkgModel" placeholder="请输入规格型号"></el-input>
            </template>
            <template v-else>{{scope.row.pkgModel}}</template>
          </template>
        </el-table-column>
        <el-table-column align="center" label="长X宽X高(cm)" min-width="200">
          <template slot-scope="scope">
            <template v-if="scope.row.isEdit">
							<div class="size-container">
								<el-input @input="calculateVolume(scope.row)" placeholder="长" v-model="scope.row.pkgLengh"></el-input>
								<span>*</span>
								<el-input @input="calculateVolume(scope.row)" placeholder="宽" v-model="scope.row.pkgWidth"></el-input>
								<span>*</span>
								<el-input @input="calculateVolume(scope.row)" placeholder="高" v-model="scope.row.pkgHeight"></el-input>
								<span>CM</span>
							</div>
            </template>
            <template v-else>{{scope.row.pkgLengh}} * {{scope.row.pkgWidth}} * {{scope.row.pkgHeight}} cm</template>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="pkgVolume" label="体积(m³)" min-width="100">
          <template slot-scope="scope">
            <template>{{scope.row.pkgVolume}}</template>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="pkgWeight" label="重量（kg）" min-width="100">
          <template slot-scope="scope">
            <template v-if="scope.row.isEdit">
              <el-input v-model="scope.row.pkgWeight" placeholder="请输入重量"></el-input>
            </template>
            <template v-else>{{scope.row.pkgWeight}}</template>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="pkgCost" label="成本(元/个)" min-width="100">
          <template slot-scope="scope">
            <template v-if="scope.row.isEdit">
              <el-input v-model="scope.row.pkgCost" placeholder="请输入成本"></el-input>
            </template>
            <template v-else>{{scope.row.pkgCost}}</template>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="remark" label="备注" min-width="150">
          <template slot-scope="scope">
            <template v-if="scope.row.isEdit">
              <el-input v-model="scope.row.remark" placeholder="请输入备注"></el-input>
            </template>
            <template v-else>{{scope.row.remark}}</template>
          </template>
        </el-table-column>
        <el-table-column align="center" fixed="right" label="操作" min-width="140">
          <template slot-scope="scope">
            <template v-if="scope.row.isEdit">
              <el-button
                @click="submitForm(scope.row)"
                size="mini"
                type="primary"
                icon="el-icon-folder-add"
              >保存</el-button>
              <el-button
                @click="handleDelete(scope.row, scope.$index)"
                size="mini"
                type="danger"
                icon="el-icon-delete"
              >{{scope.row.id?'取消':'删除'}}</el-button>
            </template>
            <template v-else>
              <el-button
                @click="handleEdit(scope.row)"
                size="mini"
                type="primary"
                icon="el-icon-edit"
              >编辑</el-button>
              <el-button
                @click="handleDelete(scope.row, scope.$index)"
                size="mini"
                type="danger"
                icon="el-icon-delete"
              >删除</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { GetList, Delete, Save, Update } from "src/api/package";
const defaultForm = {
  id: "",
  pkgType: "",
  pkgModel: "",
  pkgLengh: "",
  pkgWidth: "",
  pkgHeight: "",
  pkgVolume: "",
  pkgWeight: "",
  pkgCost: "",
  remark: "",
  isEdit: true,
};
export default {
  data() {
    return {
      params: {
        page: 1,
        limit: 50,
      },
      total: 0,
      totalPage: 1,
      tableData: []
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      GetList(this.params).then((res) => {
        let list = res.page.list;
        list.forEach((item) => {
          item.isEdit = false;
        });
        this.$set(this, "tableData", list);
        this.total = parseInt(res.page.totalCount);
        this.totalPage = parseInt(res.page.totalPage);
      });
    },
    handleEdit(row) {
      row.isEdit = true;
    },
    handleAdd() {
      this.tableData.unshift(JSON.parse(JSON.stringify(defaultForm)));
    },
    calculateVolume(row) {
      const {
        pkgLengh, pkgWidth, pkgHeight
      } = row
      row.pkgVolume = ''
      if(this.cal.isRealNum(pkgLengh) && this.cal.isRealNum(pkgWidth) && this.cal.isRealNum(pkgHeight))
      row.pkgVolume = this.cal.accDiv(this.cal.accMul(pkgLengh, this.cal.accMul(pkgWidth, pkgHeight)), 1000000).toFixed(4)
    },
    handleValidate(row) {
      const numReg = /^\d+(\.\d+)?$/;
      if (!row.pkgType) {
        this.$message({
          message: "请选择包装物类型",
          type: "error",
        });
        return false;
      }
      if (!row.pkgModel) {
        this.$message({
          message: "请填写规格型号",
          type: "error",
        });
        return false;
      }
      if (!numReg.test(row.pkgLengh)) {
        this.$message({
          message: "请填写正确的长",
          type: "error",
        });
        return false;
      }
      if (!numReg.test(row.pkgWidth)) {
        this.$message({
          message: "请填写正确的宽",
          type: "error",
        });
        return false;
      }
      if (!numReg.test(row.pkgHeight)) {
        this.$message({
          message: "请填写正确的高",
          type: "error",
        });
        return false;
      }
      if (!numReg.test(row.pkgVolume)) {
        this.$message({
          message: "请填写正确的体积",
          type: "error",
        });
        return false;
      }
      if (!numReg.test(row.pkgWeight)) {
        this.$message({
          message: "请填写正确的重量",
          type: "error",
        });
        return false;
      }
      if (!numReg.test(row.pkgCost)) {
        this.$message({
          message: "请填写正确的成本",
          type: "error",
        });
        return false;
      }
      return true;
    },
    async submitForm(row) {
      if (!this.handleValidate(row)) return;
      const res = row.id ? await Update(row) : await Save(row);
      if (res && res.code == 0) {
        this.$message({
          message: "操作成功",
          type: "success",
        });
        this.getList();
      }
    },

    handleDelete(row, index) {
      if (!row.id) {
        this.tableData.splice(index, 1);
        return;
      }
      if (row.id && row.isEdit) {
        row.isEdit = false;
        return;
      }
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
        center: true,
      })
        .then(() => {
          Delete([row.id]).then((res) => {
            if (res.code == 0) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
            }
          });
        })
        .catch(() => {});
    },

    handleSizeChange(val) {
      this.params.limit = val;
      this.getList();
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.params.page = val;
      this.getList();
      console.log(`当前页: ${val}`);
    },
    resetSearch() {
      for (let name in this.params) {
        if (name == "page") {
          this.$set(this.params, name, 1);
        } else if (name == "limit") {
          this.$set(this.params, name, 50);
        } else {
          this.$set(this.params, name, "");
        }
      }
      this.getList();
    },
  },
};
</script>
<style lang="less" scoped>
	.size-container{
		display: flex;
		align-items: center;
		>span{
			white-space: nowrap;
			padding: 0 2px;
		}
	}
</style>