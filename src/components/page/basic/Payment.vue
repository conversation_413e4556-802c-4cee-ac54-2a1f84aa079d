<template>
  <div class="p_1">
    <!-- <div>
      <el-row class="mb_1 text-right">
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </el-row>
    </div> -->
    <div class="table-container">
      <div class="mt_1 mb_1">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="params.page"
          :page-sizes="[50, 100, 300, 500]"
          :page-size="params.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
      <el-table class="table-nopadding" :data="tableData" border style="width: 100%">
        <el-table-column align="center" fixed="left" type="index" label="序号" width="60"></el-table-column>
        <el-table-column align="center" prop="payName" label="付款方式" min-width="150">
          <template slot-scope="scope">
            <template v-if="scope.row.isEdit">
							<el-input v-model="scope.row.payName" placeholder="请输入付款方式"></el-input>
            </template>
            <template v-else>{{scope.row.payName}}</template>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="remark" label="描述" min-width="100">
          <template slot-scope="scope">
            <template v-if="scope.row.isEdit">
              <el-input v-model="scope.row.remark" placeholder="请输入描述"></el-input>
            </template>
            <template v-else>{{scope.row.remark}}</template>
          </template>
        </el-table-column>
        <el-table-column align="center" label="运单支付" min-width="100">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.frightPay" disabled @change="handleStatusChange(scope.row)" :false-label="0" :true-label="1"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column align="center" label="收款单" min-width="100">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.receivable" disabled @change="handleStatusChange(scope.row)" :false-label="0" :true-label="1"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column align="center" label="付款单" min-width="100">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.receive" disabled @change="handleStatusChange(scope.row)" :false-label="0" :true-label="1"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createUserName" label="操作人" min-width="100">
        </el-table-column>
        <el-table-column align="center" prop="createTime" label="操作时间" min-width="140">
        </el-table-column>
        <!-- <el-table-column align="center" fixed="right" label="操作" min-width="140">
          <template slot-scope="scope">
            <template v-if="scope.row.isEdit">
              <el-button
                @click="submitForm(scope.row)"
                size="mini"
                type="primary"
                icon="el-icon-folder-add"
              >保存</el-button>
              <el-button
                @click="handleDelete(scope.row, scope.$index)"
                size="mini"
                type="danger"
                icon="el-icon-delete"
              >{{scope.row.id?'取消':'删除'}}</el-button>
            </template>
            <template v-else>
              <el-button
                @click="handleEdit(scope.row)"
                size="mini"
                type="primary"
                icon="el-icon-edit"
              >编辑</el-button>
              <el-button
                @click="handleDelete(scope.row, scope.$index)"
                size="mini"
                type="danger"
                icon="el-icon-delete"
              >删除</el-button>
            </template>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
  </div>
</template>

<script>
import { GetList, Delete, Save, Update } from "src/api/payment";
const defaultForm = {
  id: "",
  payName: "",
	remark: "",
	frightPay: 1,
	receivable: 1,
  receive: 1,
  isEdit: true,
};
export default {
  data() {
    return {
      params: {
        page: 1,
        limit: 50,
      },
      total: 0,
      totalPage: 1,
      tableData: [],
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      GetList(this.params).then((res) => {
        let list = res.page.list;
        list.forEach((item) => {
          item.isEdit = false;
        });
        this.$set(this, "tableData", list);
        this.total = parseInt(res.page.totalCount);
        this.totalPage = parseInt(res.page.totalPage);
      });
    },
    handleEdit(row) {
      row.isEdit = true;
    },
    handleAdd() {
      this.tableData.unshift(JSON.parse(JSON.stringify(defaultForm)));
		},
		
		handleStatusChange(row) {
			if(row.isEdit) return
			Update(row)
		},

    handleValidate(row) {
      if (!row.payName) {
        this.$message({
          message: "请输入付款方式",
          type: "error",
        });
        return false;
			}
      return true;
    },
    async submitForm(row) {
      if (!this.handleValidate(row)) return;
      const res = row.id ? await Update(row) : await Save(row);
      if (res && res.code == 0) {
        this.$message({
          message: "操作成功",
          type: "success",
        });
        this.getList();
      }
    },

    handleDelete(row, index) {
      if (!row.id) {
        this.tableData.splice(index, 1);
        return;
      }
      if (row.id && row.isEdit) {
        row.isEdit = false;
        return;
      }
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
        center: true,
      })
        .then(() => {
          Delete([row.id]).then((res) => {
            if (res.code == 0) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
            }
          });
        })
        .catch(() => {});
    },

    handleSizeChange(val) {
      this.params.limit = val;
      this.getList();
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.params.page = val;
      this.getList();
      console.log(`当前页: ${val}`);
    },
    resetSearch() {
      for (let name in this.params) {
        if (name == "page") {
          this.$set(this.params, name, 1);
        } else if (name == "limit") {
          this.$set(this.params, name, 50);
        } else {
          this.$set(this.params, name, "");
        }
      }
      this.getList();
    },
  },
};
</script>
<style lang="less" scoped>
</style>