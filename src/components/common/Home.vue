 <template>
  <div style="width: 100%; height: 100%; position: relative">
    <div class="sidebar">
      <el-scrollbar wrap-class="scrollbar-wrapper">
        <el-menu
          :default-active="onRoutes"
          :default-openeds="defaultOpeneds"
          background-color="#324057"
          text-color="#fff"
          active-text-color="#396fff"
          :collapse="isCollapse"
          class="el-menu-vertical-demo"
          unique-opened
          router
        >
          <template v-for="item in menus">
            <template v-if="item.childList">
              <el-submenu :index="item.url">
                <template slot="title">
                  <template v-if="item.icon">
                    <img
                      class="menu-icon"
                      :src="'static/img/' + item.icon + '.png'"
                      alt
                    />
                  </template>
                  <!--  <i class="el-icon-menu"></i> -->
                  <span slot="title">{{ item.name }}</span>
                </template>

                <template v-for="subItem in item.childList">
                  <el-menu-item :index="subItem.url">{{
                    subItem.name
                  }}</el-menu-item>
                </template>
              </el-submenu>
            </template>
            <template v-else>
              <el-submenu :index="item.url">
                <el-menu-item :index="item.url">
                  <i class="el-icon-menu"></i>
                  <span slot="title">{{ item.name }}</span>
                </el-menu-item>
              </el-submenu>
            </template>
          </template>
        </el-menu>
      </el-scrollbar>
    </div>
    <div class="main-content" v-bind:class="{ isCollapse: isCollapse }">
      <div class="header">
        <!-- <div class="logo" v-bind:class="{ collapse: isCollapse }">
                    <template v-if="isCollapse">
                    </template>
                    <template v-else>
                        浙江尼豪供应链@快递公司
                    </template>
        </div>-->
        <span
          class="collapseBtn"
          @click="collapseSidebar"
          :class="{ isActive: isCollapse }"
        >
          <img
            src="../../../static/img/slide.png"
            class="fl"
            style="width: 24px; height: 24px; margin: 18px"
            alt
          />
          <!-- <i class="el-icon-d-arrow-right fl" v-if="isCollapse"></i>
          <i class="el-icon-d-arrow-left fl" v-else></i>-->
        </span>
        <div class="user-info">
          <!--<div class="user-info-item">-->
          <!--<i class="el-icon-time fl" style="font-size: 20px;height:60px;line-height: 60px"></i>{{today}}-->
          <!--</div>-->
          <div class="user-info-item">
            <el-dropdown @command="handleCommand">
              <span style="font-size: 14px" class="el-dropdown-link">
                <img class="user-logo" src="../../../static/img/img.jpg" />
                {{ username }}({{ nickName }})
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">修改密码</el-dropdown-item>
                <el-dropdown-item command="2">基本信息</el-dropdown-item>
                <el-dropdown-item command="3">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="user-info-item">
            <router-link :to="{ path: '/home/<USER>/message' }">
              <el-badge :value="messageCount" class="item">
                <span class="message-count" @click="handleCommand1(1)"
                  >消息</span
                >
              </el-badge>
            </router-link>
            <!--                    <el-dropdown @command="handleCommand1">-->
            <!--                            <span class="el-dropdown-link">-->
            <!--                                <i class="el-icon-bell">-->
            <!--                                    <el-badge is-dot class="item" />-->
            <!--                                </i>-->
            <!--                            </span>-->
            <!--                        <el-dropdown-menu slot="dropdown">-->
            <!--                            <el-dropdown-item command="1">全部消息()</el-dropdown-item>-->
            <!--                        </el-dropdown-menu>-->
            <!--                    </el-dropdown>-->
          </div>
          <!-- <div class="user-info-item">
                        <span class="el-dropdown-link" @click="loginout">
                        <i class="el-icon-circle-close"></i>
                        </span>
          </div>-->
        </div>
      </div>
      <tags-view></tags-view>
      <div class="content" id="content">
        <!--<el-breadcrumb separator="/">-->
        <!--<el-breadcrumb-item v-for="item in breadcrumb">{{item}}</el-breadcrumb-item>-->
        <!--</el-breadcrumb>-->
        <!--<div class="separation-bar"></div>-->
        <transition name="move" mode="out-in">
          <!--                       <router-view v-if="['simple','expert','confirm','declared'].indexOf($route.name)>=0" :key="new Date().getTime()"/>-->
          <router-view />
        </transition>
      </div>
    </div>
    <el-dialog
      title="修改密码"
      :visible.sync="dialogVisible"
      width="30%"
      center
    >
      <el-form
        :model="forgetForm"
        :rules="forgetRules"
        ref="forgetForm"
        label-width="0px"
        class="demo-ruleForm"
      >
        <el-form-item prop="orgPassword">
          <el-input
            type="password"
            placeholder="原密码"
            auto-complete="off"
            v-model="forgetForm.orgPassword"
          ></el-input>
        </el-form-item>
        <el-form-item prop="newPassword">
          <el-input
            type="password"
            placeholder="新密码"
            auto-complete="off"
            v-model="forgetForm.newPassword"
          ></el-input>
        </el-form-item>
        <el-form-item prop="confPassword">
          <el-input
            type="password"
            placeholder="确认密码"
            auto-complete="off"
            v-model="forgetForm.confPassword"
            @keyup.enter.native="forgetPwd('forgetForm')"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="forgetPwd('forgetForm')"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <v-loading></v-loading>
  </div>
</template>
<script>
import vLoading from "./Loading.vue";
import tagsView from "./TagsView/index";
export default {
  data() {
    var validatePass = (rule, value, callback) => {
      const reg = /^(\w){6,20}$/;
      if (value === "") {
        callback(new Error("请输入密码"));
      } else if (!reg.test(value)) {
        callback(new Error("只能输入6-20个字母、数字、下划线"));
      } else {
        if (this.forgetForm.confPassword !== "") {
          this.$refs.forgetForm.validateField("confPassword");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.forgetForm.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      name: "boda",
      menus: [],
      subMenus: {},
      menuId: "",
      nickName: "",
      username: "",
      userId: "",
      wsUrl: "",
      lockReconnect: false,
      ws: "",
      tt: "",
      heartCheck: {},
      dialogVisible: false,
      forgetForm: {
        orgPassword: "",
        newPassword: "",
        confPassword: "",
      },
      forgetRules: {
        orgPassword: [
          { required: true, message: "请输入密码", trigger: "blur" },
        ],
        newPassword: [{ validator: validatePass, trigger: "blur" }],
        confPassword: [{ validator: validatePass2, trigger: "blur" }],
      },
      defaultOpeneds: [], //默认打开的菜单
    };
  },
  computed: {
    onRoutes() {
      return this.$route.path;
    },
    breadcrumb() {
      return this.$route.meta;
    },
    path() {
      return this.$route.fullPath.split("/")[1];
    },
    isCollapse() {
      return this.$store.state.isCollapse;
    },
    messageCount() {
      return this.$store.state.messageCount;
    },
    token() {
      let token = localStorage.getItem("companyToken");
      return token ? token : this.$router.replace("/login");
    },
    today() {
      let date = new Date();
      return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
    },
  },
  mounted() {
    let self = this;
    this.getMenus();
    this.getMessageCount(); //获取未读消息数量
    this.getUserInfo().then(function () {
      // if ('WebSocket' in window) {
      //     self.createWebSocket();
      // } else {
      //     self.$message({
      //         message: '当前浏览器不支持websocket,将无法收到通知'
      //     })
      // }
    });
    //心跳检测
    // this.heartCheck = {
    //     timeout: 30000,
    //     timeoutObj: null,
    //     serverTimeoutObj: null,
    //     start: function(_this){
    //         console.log('重启心跳定时器')
    //         var self = this;
    //         this.timeoutObj && clearTimeout(this.timeoutObj);
    //         this.serverTimeoutObj && clearTimeout(this.serverTimeoutObj);
    //         this.timeoutObj = setTimeout(function(){
    //             //这里发送一个心跳，后端收到后，返回一个心跳消息，
    //             console.log('发送心跳');
    //             _this.ws.send("ping");
    //             self.serverTimeoutObj = setTimeout(function() {
    //                 console.log(111);
    //                 console.log(_this.ws);
    //                 _this.ws.close();
    //                 // createWebSocket();
    //             }, self.timeout);
    //
    //         }, this.timeout)
    //     }
    // }
  },
  methods: {
    getMessageCount() {
      this.$axios.post("/courier/messageco/countUnRead").then((res) => {
        if (res.code == 0) {
          this.$store.dispatch("setMessageCount", res.count);
        }
      });
    },
    forgetPwd(formName) {
      let self = this;
      self.$refs[formName].validate((valid) => {
        if (valid) {
          self.$axios
            .post("/sys/user/editPassWord", self.forgetForm)
            .then((res) => {
              if (res.code == 0) {
                self.$message({
                  message: "密码重置成功",
                  type: "success",
                });
                self.dialogVisible = false;
                localStorage.setItem("userName", "");
                localStorage.setItem("companyToken", "");
                setTimeout(() => {
                  window.location.href = "/#/login";
                }, 1000);
              }
            });
        } else {
          return false;
        }
      });
    },
    createWebSocket() {
      let self = this;
      self.wsUrl =
        "ws://114.55.210.26:8084/manager/app/websocket/my" + self.userId;
      try {
        self.ws = new WebSocket(self.wsUrl);
        self.init();
      } catch (e) {
        console.log("catch");
        self.reconnect(self.wsUrl);
      }
    },
    init() {
      let self = this;
      self.ws.onclose = function () {
        console.log("链接关闭");
        self.reconnect(self.wsUrl);
      };
      self.ws.onerror = function () {
        console.log("发生异常了");
        self.reconnect(self.wsUrl);
      };
      self.ws.onopen = function () {
        //心跳检测重置
        self.heartCheck.start(self);
      };
      self.ws.onmessage = function (event) {
        //拿到任何消息都说明当前连接是正常的
        try {
          var data = JSON.parse(event.data);
          self.showMessage(data);
        } catch (e) {
          console.log(event.data);
        }
        self.heartCheck.start(self);
      };
    },
    reconnect(url) {
      let self = this;
      if (self.lockReconnect) {
        return;
      }
      self.lockReconnect = true;
      //没连接上会一直重连，设置延迟避免请求过多
      self.tt && clearTimeout(self.tt);
      self.tt = setTimeout(function () {
        self.createWebSocket(url);
        self.lockReconnect = false;
      }, 4000);
    },
    showMessage(data) {
      // if(!data){
      //     data = {"createTime":1559971090891,"hyId":121,"isRead":0,"messageContent":"编号为｛3453434｝的单据 BBBB ","messageTitle":"BBB ","orderId":"1212121","orderNum":"3453434","portUser":"hy121","type":1,"updateTime":1559971090891}
      // }
      const h = this.$createElement;
      let notify = this.$notify({
        title: data.messageTitle,
        message: h(
          "div",
          {
            on: {
              click: () => {
                this.handleMessageRoute(data, notify);
              },
            },
          },
          data.messageContent
        ),
        type: "info",
        duration: 0,
        onClose: function () {
          console.log(notify);
          notify = null; //清理内存
          console.log(notify);
        },
      });
    },
    handleMessageRoute(data, notify) {
      console.log("触发事件" + data);
      notify.close();
      let path,
        orderType = data.orderType,
        orderStatus = data.orderStatus;
      if (orderStatus == 500) {
        path =
          orderType == 1
            ? "/home/<USER>/commonConfirm"
            : "/home/<USER>/purchaseConfirm";
      } else if (orderStatus == 600) {
        path =
          orderType == 1
            ? "/home/<USER>/commonDeclared"
            : "/home/<USER>/purchaseDeclared";
      } else if (orderStatus > 600) {
        path =
          orderType == 1
            ? "/home/<USER>/commonDetail"
            : "/home/<USER>/purchaseDetail";
      }
      if (path) this.$router.push({ path: path, query: { id: data.orderId } });
    },
    getUserInfo() {
      return new Promise((resolve, reject) => {
        this.$axios.get("/sys/user/info").then((res) => {
          if (res.code == 0) {
            this.username = res.user.username;
            this.nickName = res.user.nickName;
            this.userId = res.user.userId;
            res.dynamicList.userId = this.userId;
            this.$store.dispatch("saveUser", res.user);
            this.$store.dispatch("saveDynamicList", res.dynamicList);
            resolve();
          }
        });
      });
    },
    handleOpen(key, keyPath) {
      console.log(key, keyPath);
    },
    handleClose(key, keyPath) {
      console.log(key, keyPath);
    },
    getMenus() {
      let self = this;
      self.$axios.get("/sys/menu/nav").then((res) => {
        res.menuList.map((item) => {
          this.defaultOpeneds.push(item.url);
        });
        self.menus = res.menuList;
      });
    },
    changeRoute(id, url) {
      this.menuId = id;
      this.menus.forEach((item) => {
        if (item.menuId == id) {
          this.subMenus = item.childList;
        }
      });
    },
    handleCommand(command) {
      if (command == 1) {
        this.dialogVisible = true;
      } else if (command == 3) {
        this.loginout();
      }
    },
    handleCommand1(command) {
      let type = command;
      this.$router.push({
        path: "/home/<USER>/message",
        query: { type: type },
      });
    },
    loginout() {
      let self = this;
      self
        .$confirm("是否确认退出?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "error",
          center: true,
        })
        .then(() => {
          self.$message({
            type: "success",
            message: "退出成功!",
          });
          // localStorage.setItem('userName','');
          localStorage.setItem("companyToken", "");
          window.location.href = "/#/login";
        })
        .catch((error) => {
          console.log(error);
        });
    },
    clearCache() {
      let self = this;
      self
        .$confirm("是否确认清除缓存?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "error",
          center: true,
        })
        .then(() => {
          self.$axios.post("/admin/cache/clearCache").then((res) => {
            if (res.status == 1) {
              self.$message({
                type: "success",
                message: "清除成功!",
              });
              setTimeout(function () {
                window.location.reload();
              }, 1000);
            }
          });
        })
        .catch((error) => {
          console.log(error);
        });
    },
    collapseSidebar() {
      this.$store.commit("CollapseSidebar");
    },
  },
  components: {
    vLoading,
    tagsView,
  },
};
</script>
<style scoped lang="less" ref="stylesheet/less">
.collapseBtn {
  cursor: pointer;
  i {
    line-height: 60px;
    padding: 0 10px;
  }
  img {
    transition: all 0.3s;
  }
}
.collapseBtn.isActive {
  img {
    transform: rotateY(180deg);
  }
}
.menu-collapse {
  position: absolute;
  top: 0;
  left: 100px;
  z-index: 2;
}
.header {
  box-sizing: border-box;
  width: 100%;
  height: 60px;
  font-size: 18px;
  line-height: 60px;
  color: #fff;
  // background: #20a0ff;
  .logo {
    float: left;
    width: 200px;
    text-align: center;
    transition: all 0.3s;
    overflow: hidden;
    height: 60px;
  }
  .logo.collapse {
    width: 64px;
  }
}

.head-menu {
  float: left;
  a {
    display: inline-block;
    height: 60px;
    line-height: 60px;
    margin: 0 20px;
    float: left;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
  }
  a.cur {
    border-bottom: 4px solid #f4f7fd;
    height: 56px;
    color: #fff;
    font-weight: 700;
  }
  a:hover {
    color: #fff;
    font-weight: 700;
  }
}
.user-info {
  float: right;
  font-size: 16px;
  color: #fff;
  .user-info-item {
    float: left;
    padding: 0 10px;
    overflow: hidden;
    i {
      margin-right: 5px;
    }
  }
  .el-dropdown-link {
    color: #fff;
    font-size: 20px;
    display: inline-block;
    cursor: pointer;
  }
  .user-logo {
    margin: 18px 10px;
    float: left;
    width: 24px;
    height: 24px;
    border-radius: 50%;
  }
}

.main-content {
  width: 100%;
  min-height: 100%;
  position: relative;
  padding-left: 200px;
  transition: all 0.3s;
  box-sizing: border-box;
}
.main-content .content {
  // background: #e5e9f2;

  .el-breadcrumb {
    padding: 10px 20px;
  }
  padding: 0px;
  > div {
    background: #fff;
  }
}
.main-content.isCollapse {
  padding-left: 64px;
}

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  bottom: 0;
  z-index: 5;
  background: #304156;
  ul {
    min-height: 100%;
    border-right: none;
  }
  .el-menu-item {
    font-size: 12px;
    height: 36px;
    line-height: 36px;
    text-indent: 10px;
  }
}
.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 200px;
  min-height: 100%;
}
.el-badge {
  height: 30px;
  margin: 15px 20px 15px 0;
  line-height: 30px;
  font-size: 12px;
  color: #fff;
}
</style>