<template>
  <div id="tags-view-container" class="tags-view-container">
    <scroll-pane ref="scrollPane" class="tags-view-wrapper" @scroll="handleScroll">
      <router-link
        ref="tag"
        key="homepage"
        :to="{ path: '/home/<USER>/list'}"
        tag="span"
        class="tags-view-item"
        @contextmenu.prevent.native="openMenu({path:'/home/<USER>/list'},$event)"
      >
        首页
      </router-link>
      <template v-for="tag in visitedViews">
        <router-link
          ref="tag"
          v-if="tag.path != '/home/<USER>/welcome'"
          :key="tag.path"
          :class="isActive(tag)?'active':''"
          :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
          tag="span"
          class="tags-view-item"
          @contextmenu.prevent.native="openMenu(tag,$event)"
        >
          {{ tag.meta[tag.meta.length - 1] }}
          <span v-if="!isActive(tag)" class="el-icon-close" @click.prevent.stop="delSelectTag(tag)" />
        </router-link>
      </template>
    </scroll-pane>
    <ul v-show="visible" :style="{left:left+'px',top:top+'px'}" class="contextmenu">
      <li v-if="isActive(selectedTag)" @click="refreshSelectedTag()">刷新</li>
      <li v-if="visitedViews.length>1" @click="closeOthersTags">关闭其它</li>
      <li v-if="visitedViews.length>1" @click="closeAllTags(selectedTag)">关闭所有</li>
    </ul>
  </div>
</template>

<script>
import ScrollPane from './ScrollPane'
import path from 'path'

export default {
  components: { ScrollPane },
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: []
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.visitedviews
    },
  },
  watch: {
    $route() {
      this.addViewTags();
    },
    visible(value) {
      if (value) {
        delete
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  mounted() {
    this.addViewTags();
  },
  methods: {
    isActive(route) {
      return route.path === this.$route.path
    },
    addViewTags(){//路由改变时执行的方法
      let flag
      if(this.visitedViews.length > 0){
        flag = this.visitedViews.length && this.visitedViews.every((item) => item.path  != this.$route.path)
      }else{
        flag = true
      }
      if(flag){
        const route = this.$route
         this.$store.dispatch('addVisitedViews',route);
      }
    }, 
    delSelectTag(route){//先提交删除数据的方法,数组删除出掉数据后，如果关闭的是当前打开的路由需要将路由改为数组最后一次push进去的路由
      this.$store.dispatch('delVisitedViews',route)
    },

    refreshSelectedTag(view) {
      this.$router.replace({
        path: '/refresh'
      })
    },
    closeOthersTags() {
      this.$store.dispatch('delAllViews')
      this.$router.push(this.selectedTag)
      this.$router.replace({
        path: '/refresh'
      })
    },
    closeAllTags(view) {
      this.$store.dispatch('delAllViews');
      this.$router.push({
        path: "/home/<USER>/list"
      });
    },
    openMenu(tag, e) {
      const menuMinWidth = 105
      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const offsetWidth = this.$el.offsetWidth // container width
      const maxLeft = offsetWidth - menuMinWidth // left boundary
      const left = e.clientX - offsetLeft + 15 // 15: margin right

      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }

      this.top = e.clientY -50
      this.visible = true
      this.selectedTag = tag
    },
    closeMenu() {
      this.visible = false
    },
    handleScroll() {
      this.closeMenu()
    }
  }
}
</script>

<style lang="less" scoped>
.tags-view-container {
  height: 34px;
  width: 100%;
  background: #fff;
  border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);
  position: relative;
  .tags-view-wrapper {
    .tags-view-item {
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 26px;
      line-height: 26px;
      border: 1px solid #d8dce5;
      color: #495060;
      background: #fff;
      padding: 0 8px;
      font-size: 12px;
      margin-left: 5px;
      margin-top: 4px;
      &:first-of-type {
        margin-left: 15px;
      }
      &:last-of-type {
        margin-right: 15px;
      }
      &.active {
        background-color: #42b983;
        color: #fff;
        border-color: #42b983;
        &::before {
          content: '';
          background: #fff;
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          position: relative;
          margin-right: 2px;
        }
      }
    }
  }
  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
    }
  }
}
</style>

<style lang="less">
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .el-icon-close {
      width: 16px;
      height: 16px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      transition: all .3s cubic-bezier(.645, .045, .355, 1);
      transform-origin: 100% 50%;
      &:before {
        transform: scale(.6);
        display: inline-block;
        vertical-align: -3px;
      }
      &:hover {
        background-color: #b4bccc;
        color: #fff;
      }
    }
  }
}
</style>
