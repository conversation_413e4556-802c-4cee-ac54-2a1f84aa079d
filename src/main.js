import Vue from 'vue';
import Vuex from 'vuex';
import App from './App';
import router from './router';
import axios from './util/axios';
import util from './util/util';
import imgPreview from './util/imgPreview';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';  // 默认主题
import "./../static/css/base.less";               //自定义样式
import "babel-polyfill";
import { status, filters } from './util/filter'
import cal from './util/calculation'

import adaptive from './directive/el-table'




import country from './components/components/country'
Vue.use(country)
import unit from './components/components/unit'
Vue.use(unit)
import channel from './components/components/channel'
Vue.use(channel)
import jn from './components/components/jn'
Vue.use(jn)
import jw from './components/components/jw'
Vue.use(jw)
import user from './components/components/user'
Vue.use(user)
import sticky from './components/components/sticky'
Vue.use(sticky)
import agent from './components/components/agent'
Vue.use(agent)
import payment from './components/components/payment'
Vue.use(payment)
import supplier from './components/components/supplier'
Vue.use(supplier).use(adaptive)

import { EditDynamicList } from 'src/api/user'

Vue.prototype.cal = cal;

Object.keys(filters).forEach(key => {
    Vue.filter(key, filters[key])
})

Vue.use(Vuex)
Vue.use(ElementUI, { size: 'small' });
Vue.use(imgPreview);
Vue.prototype.$axios = axios;
Vue.prototype.$util = util;
Vue.prototype.STATUS = status;
Vue.prototype.FILTER = filters;

Vue.config.productionTip = false

const store = new Vuex.Store({
    state: {
        user: '',
        ajaxIsLoading: false,
        isCollapse: false,
        menuParentId: '',
        menu: '',
        messageCount: 0,
        listParams: new Map(),
        visitedviews: [],
        tableConfig: {},
        dynamicList: {}
    },
    mutations: {
        ['AJAX_BEGIN_REQUEST'](state) {
            state.ajaxIsLoading = true;
        },
        ['AJAX_END_REQUEST'](state) {
            state.ajaxIsLoading = false;
        },
        ['CollapseSidebar'](state, boolean) {
            if (boolean != undefined) {
                state.isCollapse = boolean;
            } else {
                state.isCollapse = !state.isCollapse;
            }

        },
        ['SetId'](state, id) {
            state.menuParentId = id;
        },
        ['SetMenu'](state, menu) {
            state.menu = menu;
        },
        ['SAVE_LIST_PARAMS']: (state, { path, params }) => {

            state.listParams.set(path, params);
        },
        ['SetMessageCount'](state, count) {
            state.messageCount = count;
        },
        ADD_VISITED_VIEWS: (state, view) => {
            state.visitedviews.push(view)
        },
        DEL_VISITED_VIEWS: (state, view) => {
            for (let [i, v] of state.visitedviews.entries()) {
                if (v.path == view.path) {
                    state.visitedviews.splice(i, 1)
                    break
                }
            }
        },
        DEL_All_VIEWS: (state, view) => {
            state.visitedviews = []
        },
        SAVE_TABLE_CONFIG: (state, { path, config }) => {
            state.tableConfig[path] = config;
        },
        SAVE_DYNAMIC_LIST: (state, obj) => {
            state.dynamicList = obj;
            state.tableConfig = obj.content ? JSON.parse(obj.content) : {}
        },
        SAVE_USER: (state, user) => {
            state.user = user
        },
    },
    actions: {
        saveListParams: ({ commit }, { path, params }) => {
            commit('SAVE_LIST_PARAMS', { path, params });
        },
        setMessageCount: ({ commit }, count) => {
            commit('SetMessageCount', count);
        },
        addVisitedViews({ commit }, view) {//通过解构赋值得到commit方法
            commit('ADD_VISITED_VIEWS', view)//去触发ADD_VISITED_VIEWS，并传入参数
        },
        delVisitedViews({ commit }, view) {
            commit('DEL_VISITED_VIEWS', view);
        },
        delAllViews({ commit }) {
            commit('DEL_All_VIEWS');
        },
        saveTableConfig: ({ commit, state }, { path, config }) => {
            commit('SAVE_TABLE_CONFIG', { path, config });
            EditDynamicList({
                id: state.dynamicList.id || '',
                uid: state.dynamicList.userId,
                content: JSON.stringify(state.tableConfig)
            }).then(res => {
                if (res.code === 0 && !state.dynamicList.id)
                    state.dynamicList.id = res.id
            })
        },
        saveDynamicList: ({ commit }, obj) => {
            commit('SAVE_DYNAMIC_LIST', obj);
        },
        saveUser({ commit }, user) {
            commit('SAVE_USER', user);
        },
    }
});


new Vue({
    router,
    store,
    render: h => h(App)
}).$mount('#app');

