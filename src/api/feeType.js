import axios from 'src/util/axios'

export function GetList(params) {
  return axios({
    url:'/courier/jbrfee/list',
    method:'get',
    params
  })
}

export function GetFeeType() {
  return axios({
    url:'/courier/jbrfee/findFeeCost'
  })
}

export function Save(data) {
  return axios({
    url:'/courier/jbrfee/save',
    method:'post',
    data
  })
}
export function Update(data) {
  return axios({
    url:'/courier/jbrfee/update',
    method:'post',
    data
  })
}
export function Delete(data) {
  return axios({
    url:'/courier/jbrfee/delete',
    method:'delete',
    data
  })
}
export function Info(id) {
  return axios({
    url:`/courier/jbrfee/info/${id}`,
  })
}