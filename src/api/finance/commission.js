/*
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-09-01 09:23:20
 */
import axios from 'src/util/axios'

export function GetList(params) {
  return axios({
    url:'/finance/jbrbusinesscommission/list',
    method:'get',
    params
  })
}
export function Save(data) {
  return axios({
    url:'/finance/jbrbusinesscommission/save',
    method:'post',
    data
  })
}
export function Update(data) {
  return axios({
    url:'/finance/jbrbusinesscommission/update',
    method:'post',
    data
  })
}
export function Delete(data) {
  return axios({
    url:'/finance/jbrbusinesscommission/delete',
    method:'delete',
    data
  })
}
export function Info(id) {
  return axios({
    url:`/finance/jbrbusinesscommission/info/${id}`,
  })
}

/**
 * @name: 审核
 */

export function Check(data) {
  return axios({
		url:'/finance/jbrbusinesscommission/statusCheck',
		method: 'post',
		data
  })
}

/**
 * @name: 统计 月订单利润 ，增补，不可预估
 * @param businessId monthYear
 * @return {type} 
 */

export function Statistics(data) {
  return axios({
		url:`/finance/jbrbusinesscommission/sumCommission`,
		method: 'post',
		data,
    headers:{closeLoading:true}
  })
}