/*
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-09-01 09:23:20
 */
import axios from 'src/util/axios'

export function GetList(params) {
  return axios({
    url:'/courier/jbrorderinfo/settleList',
    params
  })
}

export function GetListCount(params) {
  return axios({
    url:'/courier/jbrorderinfo/settleCount',
    params
  })
}

export function GetOrderChangeLogs(params) {
  return axios({
    url:'/finance/jbrsettlelog/list',
    params
  })
}
// 运费收入
export function GetFreightIncomeList(orderId) {
  return axios({
    url: `/finance/jbrfreightincome/list/${orderId}`
  })
}

export function EditFreightIncome(data) {
  return axios({
		url: '/finance/jbrfreightincome/saveOrUpdateIncome',
		method: 'post',
		data
  })
}
// 运费成本
export function GetFreightCostList(orderId) {
  return axios({
    url: `/finance/jbrfreightcost/list/${orderId}`
  })
}

export function EditFreightCost(data) {
  return axios({
		url: '/finance/jbrfreightcost/saveOrUpdateCost',
		method: 'post',
		data
  })
}
// 物料成本
export function GetMaterialsCostList(orderId) {
  return axios({
    url: `/finance/jbrmaterialscost/list/${orderId}`
  })
}

export function EditMaterialsCost(data) {
  return axios({
		url: '/finance/jbrmaterialscost/saveOrUpdateCost',
		method: 'post',
		data
  })
}
// 不可预估
export function GetUnPredictList(orderId) {
  return axios({
    url: `/finance/jbrunpredictablecast/list/${orderId}`
  })
}

export function EditUnPredict(data) {
  return axios({
		url: '/finance/jbrunpredictablecast/saveOrUpdateCast',
		method: 'post',
		data
  })
}
// 增补费用
export function GetFeeList(orderId) {
  return axios({
    url: `/finance/jbraddorupdatefee/list/${orderId}`
  })
}

export function EditFee(data) {
  return axios({
		url: '/finance/jbraddorupdatefee/saveOrUpdateFee',
		method: 'post',
		data
  })
}

export function DisSettle(data) {
  return axios({
		url: '/finance/jbrfreightincome/disSettle',
		method: 'post',
		data
  })
}