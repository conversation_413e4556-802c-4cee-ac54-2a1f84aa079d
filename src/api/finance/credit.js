/*
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-09-01 09:23:20
 */
import axios from 'src/util/axios'

export function GetList(params) {
  return axios({
    url:'/finance/jbrbusiness/list',
    method:'get',
    params
  })
}
export function Save(data) {
  return axios({
    url:'/finance/jbrbusiness/save',
    method:'post',
    data
  })
}
export function Update(data) {
  return axios({
    url:'/finance/jbrbusiness/update',
    method:'post',
    data
  })
}
export function Delete(data) {
  return axios({
    url:'/finance/jbrbusiness/delete',
    method:'delete',
    data
  })
}
export function Info(id) {
  return axios({
    url:`/finance/jbrbusiness/info/${id}`,
  })
}
export function Log(params) {
  return axios({
    url:'/finance/jbrbusinesslog/list',
    params
  })
}
/**
 * @name: 查询业务代理
 * @param nickName 
 * @param userName 
 * @return {type} 
 */

export function GetBusinessAgent(data) {
  return axios({
		url:'/finance/jbrbusiness/findBusiness',
    method: 'post',
    data,
    headers:{closeLoading:true}
  })
}