/*
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-09-01 09:23:20
 */
import axios from 'src/util/axios'

export function GetList(params) {
  return axios({
    url:'/finance/jbrbusinessreceive/list',
    method:'get',
    params
  })
}
export function Save(data) {
  return axios({
    url:'/finance/jbrbusinessreceive/save',
    method:'post',
    data
  })
}
export function Update(data) {
  return axios({
    url:'/finance/jbrbusinessreceive/update',
    method:'post',
    data
  })
}
export function Delete(data) {
  return axios({
    url:'/finance/jbrbusinessreceive/delete',
    method:'delete',
    data
  })
}
export function Info(id) {
  return axios({
    url:`/finance/jbrbusinessreceive/info/${id}`,
  })
}

export function Check(data) {
  return axios({
		url:'/finance/jbrbusinessreceive/statusCheck',
		method: 'post',
		data
  })
}