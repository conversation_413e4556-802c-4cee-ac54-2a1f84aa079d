/*
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-09-01 09:23:20
 */
import axios from 'src/util/axios'

export function GetList(params) {
  return axios({
    url:'/finance/jbrsupplierreceive/list',
    method:'get',
    params
  })
}
export function Save(data) {
  return axios({
    url:'/finance/jbrsupplierreceive/save',
    method:'post',
    data
  })
}
export function Update(data) {
  return axios({
    url:'/finance/jbrsupplierreceive/update',
    method:'post',
    data
  })
}
export function Delete(data) {
  return axios({
    url:'/finance/jbrsupplierreceive/delete',
    method:'delete',
    data
  })
}
export function Info(id) {
  return axios({
    url:`/finance/jbrsupplierreceive/info/${id}`,
  })
}

export function Check(data) {
  return axios({
		url:'/finance/jbrsupplierreceive/statusCheck',
		method: 'post',
		data
  })
}