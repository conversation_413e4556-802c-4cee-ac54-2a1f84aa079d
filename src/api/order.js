/*
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-09-04 10:20:23
 */
import axios from 'src/util/axios'

export function Info(orderId) {
  return axios({
    url:`/courier/jbrorderinfo/info/${orderId}`
  })
}

export function CloseOrder(data) {
  return axios({
    url:'/courier/jbrorderinfo/closeOrder',
    method:'post',
    data
  })
}

export function CopyOrder(orderId) {
  return axios({
    url: `/courier/jbrorderinfo/copyOrder/${orderId}`
  })
}

export function SaveOrderIssue(data) {
  return axios({
    url:'/courier/jbrorderissuelog/save',
    method:'post',
    data
  })
}

export function GetOrdIssueList(orderId) {
  return axios({
    url:'/courier/jbrorderissuelog/getOrdIssueList',
    method:'post',
    data: { 
			orderId
		}
  })
}

export function EditOrderAddress(data) {
  return axios({
    url:'/courier/jbrorderinfo/editOrderAddress',
    method:'post',
    data
  })
}

export function SaveOrderBack(data) {
  return axios({
    url:'/courier/jbrorderbacklog/save',
    method:'post',
    data
  })
}

export function UpdateOrderSettleStatus(data) {
  return axios({
    url:'/courier/jbrorderinfo/updateSettleStatus',
    method:'post',
    data
  })
}

export function EditLogistics(data) {
  return axios({
    url:'/courier/jbrorderpeoplelog/saveAndUpdate',
    method: 'post',
    data
  })
}

export function GetLogistics(ordeId) {
  return axios({
    url:`/courier/jbrorderpeoplelog/info/${ordeId}`
  })
}

export function GetJnDefault() {
  return axios({
    url: '/courier/jbrjndeliveruser/getDefaule'
  })
}

export function GetJwDefault() {
  return axios({
    url: '/courier/jbrjwdeliveruser/getDefaule'
  })
}

export function GetOrderLog(orderId, closeLoading = true) {
  return axios({
    url: `/courier/jbrorderremarklog/list?orderId=${orderId}`,
    headers: {closeLoading}
  })
}
