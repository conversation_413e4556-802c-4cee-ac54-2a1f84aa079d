/*
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-08-27 15:59:31
 */
import axios from 'src/util/axios'

export function GetList(params) {
  return axios({
    url:'/courier/jbrpaytype/list',
    method:'get',
    params
  })
}
export function Save(data) {
  return axios({
    url:'/courier/jbrpaytype/save',
    method:'post',
    data
  })
}
export function Update(data) {
  return axios({
    url:'/courier/jbrpaytype/update',
    method:'post',
    data
  })
}
export function Delete(data) {
  return axios({
    url:'/courier/jbrpaytype/delete',
    method:'delete',
    data
  })
}
export function Info(id) {
  return axios({
    url:`/courier/jbrpaytype/info/${id}`,
  })
}

/**
 * @name: 检测 提成抵扣的付款方式 是否开启了收款单  
 * @return isEnable  true 开启 false 关闭 
 */
export function CheckCommissionReceiveIsEnable() {
  return axios({
    url: '/courier/jbrpaytype/checkCommissionReceiveIsEnable',
    method:'post'
  })
}

/**   
 * @name 查询当前可用支付方式 
 * @param payType  页面类型 1 运单支付 2 收款单 3 付款单
 */
export function GetAllPayment(params, closeLoading = false) {
  return axios({
    url: '/courier/jbrpaytype/all',
    params,
    headers: {closeLoading}
  })
}



