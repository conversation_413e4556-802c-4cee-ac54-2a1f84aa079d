/*
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-09-02 09:18:02
 */
import axios from 'src/util/axios'

export function GetList(params, closeLoading = false) {
  return axios({
    url:'/courier/jbrsupplier/list',
    method:'get',
    params,
    headers: {closeLoading}
  })
}

export function GetListByType(data, closeLoading = false) {
  return axios({
    url:'/courier/jbrsupplier/findSupplierByType',
    method:'POST',
    data,
    headers: {closeLoading}
  })
}
export function Save(data) {
  return axios({
    url:'/courier/jbrsupplier/save',
    method:'post',
    data
  })
}
export function Update(data) {
  return axios({
    url:'/courier/jbrsupplier/update',
    method:'post',
    data
  })
}
export function Delete(data) {
  return axios({
    url:'/courier/jbrsupplier/delete',
    method:'delete',
    data
  })
}
export function Info(id, closeLoading = false) {
  return axios({
    url:`/courier/jbrsupplier/info/${id}`,
    headers: {closeLoading}
  })
}