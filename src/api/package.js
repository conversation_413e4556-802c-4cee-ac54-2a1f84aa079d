/*
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-08-26 15:47:25
 */
import axios from 'src/util/axios'
export function GetList(params) {
  return axios({
    url:'/courier/jbrpackage/list',
    method:'get',
    params
  })
}
export function Save(data) {
  return axios({
    url:'/courier/jbrpackage/save',
    method:'post',
    data
  })
}
export function Update(data) {
  return axios({
    url:'/courier/jbrpackage/update',
    method:'post',
    data
  })
}
export function Delete(data) {
  return axios({
    url:'/courier/jbrpackage/delete',
    method:'delete',
    data
  })
}
export function Info(id) {
  return axios({
    url:`/courier/jbrpackage/info/${id}`,
  })
}
export function GetByPkgType(pkgType) {
  return axios({
    url:`/courier/jbrpackage/getByPkgType?pkgType=${pkgType}`,
  })
}

