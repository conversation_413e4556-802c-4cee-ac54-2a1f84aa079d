/*
 * @Descripttion: 
 * @version: 
 * @Author: huakailei
 * @Date: 2020-08-31 09:35:57
 */
import axios from 'src/util/axios'

/**
 * @name: 当日发货毛利
 */
export function TodayProfit(params) {
  return axios({
    url:'/sys/jbrReport/todayProfit',
    method:'get',
    params
  })
}

/**
 * @name: 订单利润表
 */
export function OrderProfit(params) {
  return axios({
    url:'/sys/jbrReport/orderProfit',
    method:'get',
    params
  })
}

/**
 * @name: 月度订单发货统计
 */
export function OrderSend(params) {
  return axios({
    url:'/sys/jbrReport/orderSend',
    method:'get',
    params
  })
}

/**
 * @name: 业务代理月订单赔偿统计
 */
export function OrderCompensation(params) {
  return axios({
    url:'/sys/jbrReport/orderCompensation',
    method:'get',
    params
  })
}

/**
 * @name: 提成
 */
export function BusinessCommission(params) {
  return axios({
    url:'/sys/jbrReport/businessCommission',
    method:'get',
    params
  })
}

/**
 * @name: 业务代理应收汇总
 */
export function BusinessIncome(params) {
  return axios({
    url:'/sys/jbrReport/businessIncome',
    method:'get',
    params
  })
}

/**
 * @name: 客户应收明细表
 */
export function OrderClient(params) {
  return axios({
    url:'/sys/jbrReport/orderClient',
    method:'get',
    params
  })
}
export function OrderClientSum(params) {
  return axios({
    url:'/sys/jbrReport/orderClientSum',
    method:'get',
    params
  })
}

/**
 * @name: 业务代理对账单
 */
export function BusinessReconciliation(params) {
  return axios({
    url:'/sys/jbrReport/businessReconciliation',
    method:'get',
    params
  })
}

/**
 * @name: 供应商应付汇总
 */
export function SupplierIncome(params) {
  return axios({
    url:'/sys/jbrReport/supplierIncome',
    method:'get',
    params
  })
}

/**
 * @name: 供应商对账单
 */
export function SupplierReconciliation(params) {
  return axios({
    url:'/sys/jbrReport/supplierReconciliation',
    method:'get',
    params
  })
}