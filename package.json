{"name": "trade", "version": "2.1.0", "description": "基于Vue.js 2.x系列 + element-ui 内容管理系统解决方案", "author": "huakailei <<EMAIL>>", "private": true, "scripts": {"dev": "node build/dev-server.js", "build": "node build/build.js", "build:dll": "webpack --config build/webpack.dll.conf.js"}, "dependencies": {"axios": "^0.15.3", "element-ui": "^2.13.2", "sortablejs": "^1.8.3", "vue": "^2.3.2", "vue-router": "^2.3.1", "vuex": "^3.0.1", "wangeditor": "^3.1.1"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-loader": "^6.2.10", "babel-plugin-transform-runtime": "^6.22.0", "babel-polyfill": "^6.23.0", "babel-preset-es2015": "^6.22.0", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "compression-webpack-plugin": "^1.1.11", "connect-history-api-fallback": "^1.3.0", "css-loader": "^0.28.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "function-bind": "^1.1.0", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "less": "^3.8.1", "less-loader": "^4.1.0", "opn": "^4.0.2", "ora": "^1.2.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "url-loader": "^1.1.1", "vue-loader": "^11.3.4", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.2.6", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}